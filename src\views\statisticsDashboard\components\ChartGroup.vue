<template>
  <!-- :class="{
      'opacity-80 transform rotate-1 cursor-grabbing': isDragging,
      'fixed inset-0 z-1000 rounded-none': isFullscreen,
    }" -->
  <LazyChartContainer
    :chart-id="config?.id || props.chartId"
    :chart-title="config?.title || '图表'"
    :lazy-options="lazyLoadOptions"
    :min-height="chartHeight"
    class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
  >
    <template #default>
      <!-- 图表头部 -->
      <div v-if="showHeader" class="flex justify-between items-center p-3 border-b border-gray-100 bg-gray-50">
        <div class="flex items-center gap-3">
          <div v-if="draggable" class="cursor-grab text-gray-400 hover:text-gray-600 active:cursor-grabbing" @mousedown="handleDragStart">
            <Icon icon="ant-design:drag-outlined" />
          </div>
          <div class="flex gap-1">
            <a-tag v-if="config?.drillDown?.enabled" color="blue" size="small"> 支持下探 </a-tag>
            <a-tag v-if="config?.type" :color="getChartTypeColor(config.type)" size="small">
              {{ getChartTypeName(config.type) }}
            </a-tag>
          </div>
        </div>

        <div>
          <a-space size="small">
            <a-tooltip title="刷新数据">
              <a-button type="text" size="small" :loading="isChartLoading" @click="handleRefresh">
                <Icon icon="ant-design:reload-outlined" />
              </a-button>
            </a-tooltip>

            <a-tooltip title="全屏显示">
              <a-button type="text" size="small" @click="handleFullscreen">
                <Icon icon="ant-design:fullscreen-outlined" />
              </a-button>
            </a-tooltip>

            <a-dropdown>
              <a-button type="text" size="small">
                <Icon icon="ant-design:more-outlined" />
              </a-button>
              <template #overlay>
                <a-menu @click="handleMenuClick">
                  <a-menu-item key="export-png">
                    <Icon icon="ant-design:picture-outlined" />
                    导出为PNG
                  </a-menu-item>
                  <a-menu-item key="export-pdf">
                    <Icon icon="ant-design:file-pdf-outlined" />
                    导出为PDF
                  </a-menu-item>
                  <a-menu-item key="copy-data">
                    <Icon icon="ant-design:copy-outlined" />
                    复制数据
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="settings">
                    <Icon icon="ant-design:setting-outlined" />
                    图表设置
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </div>
      </div>

      <!-- 自定义标题区（仅对可切换图表显示，且不在下探状态时显示） -->
      <div class="flex justify-center py-1 border-b border-gray-100">
        <!-- 非下探状态时显示切换标题，点击切换标题 -->
        <template v-if="!isInDrillDownState && config?.customProps?.switchable">
          <div class="cursor-pointer px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-50" @click="handleTitleSwitch">
            <div class="flex items-center gap-2">
              <span class="font-medium text-blue-600">{{ config?.title }}</span>
              <span class="text-gray-400">/</span>
              <span class="font-medium text-gray-500 hover:text-blue-500 transition-colors">{{ config?.customProps?.alternativeTitle }}</span>
              <Icon icon="ant-design:swap-outlined" class="ml-2 text-sm text-blue-500" />
            </div>
          </div>
        </template>
        <!-- 下探状态或者非可切换图表时显示当前标题，不显示切换标题 -->
        <template v-else>
          <div class="cursor-pointer px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-50">
            <div class="flex items-center gap-2">
              <span class="font-medium text-blue-600">{{ config?.title }}</span>
            </div>
          </div>
        </template>
      </div>

      <!-- 图表内容区 -->
      <div class="relative" :style="{ height: chartHeight }">
        <!-- Loading状态 -->
        <div v-if="isChartLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div class="flex flex-col items-center gap-3">
            <a-spin size="large" />
            <div class="text-gray-600 text-sm">{{ refreshing ? '正在刷新数据...' : '正在加载图表...' }}</div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="!data || data.length === 0" class="flex items-center justify-center h-full">
          <a-empty description="暂无数据">
            <template #image>
              <Icon icon="ant-design:bar-chart-outlined" style="font-size: 48px; color: #d9d9d9" />
            </template>
            <a-button type="primary" size="small" @click="handleRefresh"> 重新加载 </a-button>
          </a-empty>
        </div>

        <!-- 图表内容 -->
        <div v-else class="h-full" :class="{ 'opacity-50': isChartLoading }">
          <EChartsComponent
            v-if="config"
            ref="chartRef"
            :config="config"
            :data="data"
            :height="chartHeight"
            :width="chartWidth"
            :draggable="draggable"
            :is-dragging="isDragging"
          />
        </div>
      </div>
    </template>
  </LazyChartContainer>
</template>

<script lang="ts">
  export default {
    name: 'ChartGroup',
  };
</script>
<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import EChartsComponent from './EChartsComponent.vue';
  import LazyChartContainer from './LazyChartContainer.vue';
  // DrillDownState 类型不再需要，通过上下文系统管理
  import { useChartActionsOptional, useChartConfigOptional, useChartDataOptional } from '../hooks/useChartActions';
  import { message } from 'ant-design-vue';
  import { normalizeSizeValue } from '../utils';

  // Props定义 - 简化为只接收chartId，配置和数据从上下文系统获取
  interface Props {
    /** 图表ID */
    chartId: string;
    /** 是否可拖拽 */
    draggable?: boolean;
    /** 是否显示头部 */
    showHeader?: boolean;
    /** 图表高度 */
    height?: string;
    /** 图表宽度 */
    width?: string;
    /** 外部loading状态 */
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    draggable: false,
    showHeader: false,
    height: '450px',
    width: '100%',
    loading: false,
  });

  // 懒加载配置 - 暂时注释，用于问题排查
  const lazyLoadOptions = computed(() => ({
    rootMargin: '100px',
    threshold: 0.1,
    once: true,
    delay: Math.random() * 300 + 100, // 随机延迟，避免同时加载
  }));

  // 注意：所有事件现在都通过上下文系统处理，不再需要emit

  // 使用图表上下文系统
  const chartActions = useChartActionsOptional();
  const chartConfig = useChartConfigOptional();
  const chartData = useChartDataOptional();

  // 从上下文系统获取配置和数据
  const config = computed(() => {
    return chartConfig.getChartConfig(props.chartId);
  });

  const data = computed(() => {
    const cfg = config.value;
    if (!cfg) {
      return [];
    }

    const chartDataResult = chartData.getChartData(cfg.dataSource);
    console.log(`ChartGroup[${props.chartId}]: 数据获取 - 数据源: ${cfg.dataSource}, 数据长度: ${chartDataResult?.length || 0}`);

    return chartDataResult || [];
  });

  // 本地状态
  const chartRef = ref();
  const refreshing = ref(false);
  const isDragging = ref(false);
  const isFullscreen = ref(false);

  // 计算属性：综合loading状态
  const isChartLoading = computed(() => {
    return props.loading || refreshing.value;
  });

  // 计算属性
  const chartHeight = computed(() => {
    if (isFullscreen.value) return '80vh';

    // 优先使用配置中的尺寸，如果没有则使用props
    const cfg = config.value;
    if (cfg?.size?.height !== undefined) {
      const normalizedHeight = normalizeSizeValue(cfg.size.height);
      if (normalizedHeight) {
        return normalizedHeight;
      }
    }

    return props.height;
  });

  const chartWidth = computed(() => {
    if (isFullscreen.value) return '100%';

    // 优先使用配置中的尺寸，如果没有则使用props
    const cfg = config.value;
    if (cfg?.size?.width !== undefined) {
      const normalizedWidth = normalizeSizeValue(cfg.size.width);
      if (normalizedWidth) {
        return normalizedWidth;
      }
    }

    return props.width;
  });

  // 判断是否在下探状态 - 使用上下文系统
  const isInDrillDownState = computed(() => {
    const drillDownState = chartConfig.getChartDrillDownState(props.chartId);
    return drillDownState?.isInDrillDown || false;
  });

  /**
   * 获取图表类型颜色
   */
  const getChartTypeColor = (type: string) => {
    const colors = {
      bar: 'blue',
      pie: 'green',
      line: 'orange',
      scatter: 'purple',
      radar: 'cyan',
      funnel: 'magenta',
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  /**
   * 获取图表类型名称
   */
  const getChartTypeName = (type: string) => {
    const names = {
      bar: '柱状图',
      pie: '饼图',
      line: '折线图',
      scatter: '散点图',
      radar: '雷达图',
      funnel: '漏斗图',
    };
    return names[type as keyof typeof names] || '未知';
  };

  /**
   * 格式化时间
   */
  // const formatTime = (time: Date) => {
  //   return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
  // };

  // 注意：图表事件处理已移至 EChartsComponent 中直接使用上下文系统
  // ChartGroup 不再需要处理这些事件，因为 EChartsComponent 直接与上下文系统通信

  /**
   * 处理标题切换
   */
  const handleTitleSwitch = () => {
    const cfg = config.value;
    if (!cfg?.customProps?.switchable) return;

    const newDataSource = cfg.customProps.alternativeDataSource;
    const newTitle = cfg.customProps.alternativeTitle;

    if (newDataSource && newTitle) {
      // 直接使用inject的上下文，避免事件传递
      chartActions.switchChartDataSource(cfg.id, newDataSource, newTitle);
    }
  };

  /**
   * 处理刷新 - 专注于数据刷新而不是重新加载组件
   */
  const handleRefresh = async () => {
    if (isChartLoading.value) return; // 防止重复刷新

    refreshing.value = true;

    try {
      // 直接使用上下文系统刷新图表
      const cfg = config.value;
      if (cfg) chartActions.refreshChart(cfg.id);

      // 等待一个合理的时间让数据刷新完成
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 数据刷新现在完全由上下文系统处理，组件会自动响应数据变化
    } catch (error) {
      console.error('刷新图表数据失败:', error);
      const cfg = config.value;
      message.error(`刷新 ${cfg?.title || '图表'} 失败`);
    } finally {
      // 延迟重置loading状态，确保用户能看到刷新效果
      setTimeout(() => {
        refreshing.value = false;
      }, 300);
    }
  };

  /**
   * 处理全屏
   */
  const handleFullscreen = () => {
    const cfg = config.value;
    if (!cfg) return;

    isFullscreen.value = !isFullscreen.value;
    // 直接使用上下文系统处理全屏
    chartActions.fullscreenChart(cfg.id);
  };

  /**
   * 处理菜单点击
   */
  const handleMenuClick = ({ key }: { key: string }) => {
    const cfg = config.value;
    if (!cfg) return;

    switch (key) {
      case 'export-png':
      case 'export-pdf':
        // 直接使用上下文系统处理导出
        chartActions.exportChart(cfg.id, key.split('-')[1]);
        break;
      case 'copy-data':
        copyDataToClipboard();
        break;
      case 'settings':
        // 设置功能暂时保留console输出，后续可扩展上下文系统
        console.log('打开图表设置:', cfg.id);
        message.info('设置功能开发中...');
        break;
    }
  };

  /**
   * 复制数据到剪贴板
   */
  const copyDataToClipboard = () => {
    const currentData = data.value;
    const dataText = JSON.stringify(currentData, null, 2);
    navigator.clipboard
      .writeText(dataText)
      .then(() => {
        message.success('数据已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败');
      });
  };

  /**
   * 处理拖拽开始
   */
  const handleDragStart = (_event: MouseEvent) => {
    if (!props.draggable) return;

    const cfg = config.value;
    if (!cfg) return;

    isDragging.value = true;
    console.log('开始拖拽图表:', cfg.id);

    // 这里可以添加拖拽逻辑
    const handleMouseUp = () => {
      isDragging.value = false;
      console.log('结束拖拽图表:', cfg.id);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mouseup', handleMouseUp);
  };

  // 移除了下探状态的监听，现在通过上下文系统直接获取
</script>
