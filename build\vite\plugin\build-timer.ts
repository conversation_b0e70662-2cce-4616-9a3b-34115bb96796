import type { Plugin } from 'vite';

interface TimingData {
  name: string;
  start: number;
  end?: number;
  duration?: number;
}

export function buildTimerPlugin(): Plugin {
  const timings: TimingData[] = [];
  const phaseTimings = new Map<string, number>();
  let buildStart: number;
  let currentPhase: string | null = null;

  const addTiming = (name: string) => {
    const now = Date.now();
    
    // 结束当前阶段
    if (currentPhase) {
      const existing = timings.find(t => t.name === currentPhase && !t.end);
      if (existing) {
        existing.end = now;
        existing.duration = now - existing.start;
        phaseTimings.set(currentPhase, existing.duration);
      }
    }
    
    // 开始新阶段
    currentPhase = name;
    timings.push({ name, start: now });
  };

  const finishCurrentPhase = () => {
    if (currentPhase) {
      const now = Date.now();
      const existing = timings.find(t => t.name === currentPhase && !t.end);
      if (existing) {
        existing.end = now;
        existing.duration = now - existing.start;
        phaseTimings.set(currentPhase, existing.duration);
      }
      currentPhase = null;
    }
  };

  return {
    name: 'build-timer',
    
    buildStart() {
      buildStart = Date.now();
      console.log('\n🚀 构建开始...');
      addTiming('构建初始化');
    },
    
    buildEnd() {
      finishCurrentPhase();
      const totalTime = Date.now() - buildStart;
      
      console.log('\n✅ 构建完成');
      console.log(`📊 总耗时: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}s)`);
      
      // 输出详细时间分析
      const validTimings = Array.from(phaseTimings.entries())
        .filter(([, duration]) => duration > 0)
        .sort(([, a], [, b]) => b - a);
      
      if (validTimings.length > 0) {
        console.log('\n⏱️ 各阶段耗时分析:');
        console.table(validTimings.map(([phase, duration]) => ({
          阶段: phase,
          耗时: `${duration}ms`,
          占比: `${((duration / totalTime) * 100).toFixed(1)}%`
        })));
      }
      
      // 性能建议
      if (totalTime > 30000) {
        console.log('\n💡 性能建议: 构建时间超过30秒，建议检查依赖和插件配置');
      } else if (totalTime > 15000) {
        console.log('\n💡 性能建议: 构建时间较长，可以考虑优化大型依赖');
      } else {
        console.log('\n✨ 构建性能良好！');
      }
    },

    configResolved() {
      addTiming('配置解析');
    },

    options() {
      addTiming('选项处理');
    },

    resolveId(id, importer) {
      if (!importer && id.includes('node_modules')) {
        // 只在第一次解析依赖时记录
        if (!phaseTimings.has('依赖解析')) {
          addTiming('依赖解析');
        }
      }
    },

    load(id) {
      if (id.includes('node_modules') && !phaseTimings.has('依赖加载')) {
        addTiming('依赖加载');
      }
    },

    transform(code, id) {
      if (id.endsWith('.vue') && !phaseTimings.has('Vue SFC 编译')) {
        addTiming('Vue SFC 编译');
      } else if ((id.endsWith('.ts') || id.endsWith('.tsx')) && !phaseTimings.has('TypeScript 编译')) {
        addTiming('TypeScript 编译');
      } else if (id.endsWith('.css') && !phaseTimings.has('CSS 处理')) {
        addTiming('CSS 处理');
      }
    },

    generateBundle() {
      addTiming('生成 Bundle');
    },

    writeBundle() {
      addTiming('写入文件');
    }
  };
}
