# 构建性能监控指南

## 概述

本项目集成了完整的构建性能监控系统，帮助开发团队分析和优化构建性能。

## 🔧 监控组件

### 1. 构建时间监控 (buildTimerPlugin)
- **功能**: 监控构建各个阶段的耗时
- **输出**: 详细的阶段时间分析表格
- **位置**: `build/vite/plugin/build-timer.ts`

### 2. 依赖加载监控 (depsTimerPlugin)
- **功能**: 分析依赖包的加载时间
- **输出**: Top 10 最慢依赖列表
- **位置**: `build/vite/plugin/deps-timer.ts`

### 3. 系统资源监控 (systemMonitorPlugin)
- **功能**: 监控内存使用、CPU 信息
- **输出**: 系统资源使用报告
- **位置**: `build/vite/plugin/system-monitor.ts`

### 4. 构建产物分析 (bundleAnalyzerPlugin)
- **功能**: 分析构建产物大小和压缩率
- **输出**: 文件大小统计和优化建议
- **位置**: `build/vite/plugin/bundle-analyzer.ts`

## 📊 使用方法

### 基础构建监控
```bash
# 正常构建，会自动显示监控信息
pnpm build
```

### 详细构建分析
```bash
# 运行完整的构建性能分析
pnpm run build:analyze
```

### 开发环境监控
```bash
# 开发服务器也会显示基础监控信息
pnpm dev
```

## 📈 监控输出示例

### 构建时间分析
```
⏱️ 各阶段耗时分析:
┌─────────┬──────────────────┬────────┬────────┐
│ (index) │       阶段       │  耗时  │  占比  │
├─────────┼──────────────────┼────────┼────────┤
│    0    │ 'TypeScript 编译' │ '8500ms' │ '30.2%' │
│    1    │   '生成 Bundle'   │ '6200ms' │ '22.1%' │
│    2    │   '依赖解析'     │ '4800ms' │ '17.1%' │
└─────────┴──────────────────┴────────┴────────┘
```

### 依赖加载分析
```
📦 依赖加载时间分析 (Top 10):
┌─────────┬──────────────────┬──────────┬──────────┐
│ (index) │     依赖名称     │ 加载时间 │ 代码大小 │
├─────────┼──────────────────┼──────────┼──────────┤
│    0    │ 'ant-design-vue' │ '2500ms' │ '850.2KB' │
│    1    │    'echarts'     │ '1800ms' │ '1.2MB'  │
│    2    │     'tinymce'    │ '1200ms' │ '650.5KB' │
└─────────┴──────────────────┴──────────┴──────────┘
```

### 系统资源分析
```
💻 系统资源使用分析:
CPU 核心数: 8
总内存: 16.00 GB
可用内存: 8.50 GB
构建内存使用: 245.67 MB
峰值内存使用: 512.34 MB
```

### 构建产物分析
```
📦 构建产物分析:
┌─────────┬──────────────────┬──────────┬──────────┬────────┐
│ (index) │     文件名       │ 原始大小 │ Gzip大小 │ 压缩率 │
├─────────┼──────────────────┼──────────┼──────────┼────────┤
│    0    │ 'antd-vue-vendor'│ '1.44MB' │ '420KB'  │ '70.8%'│
│    1    │ 'index-CC8Hex9Y' │ '631KB'  │ '185KB'  │ '70.7%'│
└─────────┴──────────────────┴──────────┴──────────┴────────┘
```

## 🎯 性能优化建议

### 基于监控结果的优化策略

#### 1. 构建时间优化
- **TypeScript 编译慢**: 启用增量编译，优化 tsconfig.json
- **依赖解析慢**: 使用 optimizeDeps.include 预构建大型依赖
- **打包阶段慢**: 检查 manualChunks 配置，避免单个包过大

#### 2. 依赖优化
- **大型依赖**: 考虑 CDN 化或按需加载
- **重复依赖**: 检查是否有功能重复的包
- **未使用依赖**: 定期使用 depcheck 清理

#### 3. 系统资源优化
- **内存使用高**: 增加 Node.js 内存限制或优化大型依赖
- **CPU 利用率低**: 启用并行构建选项

#### 4. 构建产物优化
- **文件过大**: 进一步拆分或使用动态导入
- **压缩率低**: 检查是否包含已压缩内容

## 🔍 高级分析

### 依赖时间 API
开发环境下可访问：`http://localhost:3101/api/deps-timing`

返回 JSON 格式的依赖加载时间数据：
```json
{
  "dependencies": [
    {
      "name": "ant-design-vue",
      "loadTime": 2500,
      "size": 870400,
      "firstLoad": 1640995200000
    }
  ],
  "totalDeps": 45,
  "totalLoadTime": 15600
}
```

### 自定义监控
可以在插件中添加自定义监控逻辑：

```typescript
// 在 build-timer.ts 中添加自定义阶段
addTiming('自定义阶段');
```

## 📋 监控数据存储

### 构建分析结果
- **文件**: `build-results.json`
- **内容**: 完整的构建分析数据
- **用途**: 历史对比、趋势分析

### 构建日志
- **文件**: `build-analysis.log`
- **内容**: 详细的构建过程日志
- **用途**: 问题排查、深度分析

## 🚀 持续优化

### 建立性能基线
1. 记录当前构建时间作为基线
2. 每次优化后对比性能变化
3. 建立性能回归检测机制

### 定期性能审查
1. 每月运行构建分析
2. 识别性能回归问题
3. 制定优化计划

### 团队协作
1. 分享性能监控结果
2. 建立性能优化最佳实践
3. 培训团队使用监控工具

## 🛠️ 故障排除

### 常见问题

#### 监控插件不工作
- 检查插件是否正确导入
- 确认 Vite 配置中已添加插件
- 查看控制台是否有错误信息

#### 内存使用过高
- 增加 Node.js 内存限制：`NODE_OPTIONS=--max-old-space-size=8192`
- 检查是否有内存泄漏
- 优化大型依赖的处理

#### 构建时间异常
- 清理构建缓存：`pnpm clean:cache`
- 检查系统资源使用情况
- 分析是否有网络依赖问题

## 📞 技术支持

如果遇到监控相关问题，请：
1. 查看构建日志文件
2. 检查系统资源使用情况
3. 提供详细的错误信息和环境配置

---

**最后更新**: 2025-07-17  
**维护人员**: 开发团队
