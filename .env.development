# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 跨域代理，您可以配置多个 ,请注意，没有换行符
# 如祺开发环境
VITE_PROXY = [["/manage","https://cn-iop-api-dev.gac-international.com/manage"],["/upload","http://localhost:3300/upload"], ['/data/statistics', "https://cn-iop-api-dev.gac-international.com/data/statistics"]]
# 链接后端本地调试开发
# VITE_PROXY = [["/manage","https://cn-iop-api-dev.gac-international.com/manage"], ["/data/statistics","http://************:8100"]] 统计看板
# ************(z<PERSON><PERSON>) ************(chenlong) - 后端IP 108(yeqiu)

# 后台接口全路径地址(必填)-链接后端本地调试开发
# VITE_GLOB_DOMAIN_URL=http://************:8090/manage/iop
# VITE_GLOB_DOMAIN_URL=http://************:8090/manage/iop
# 其他单独接口使用（非项目中配置的 axios）
VITE_GLOB_DOMAIN_URL=/manage/base
# VITE_GLOB_DOMAIN_URL=http://***********:8090/manage/base

# 后台接口父地址(必填)
# 如祺开发环境-BaseUrl
VITE_GLOB_API_URL=/manage/base/manage/iop
# 链接后端本地调试开发-BaseUrl
# VITE_GLOB_API_URL=/manage/iop

# 子应用接口路径前缀
VITE_CHILD_API_URL=/manage

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_gac_iop_childapp=//localhost:3101/gac_iop_childapp/
# VITE_APP_SUB_gac_iop_question=//localhost:3102/gac_iop_question/

# 填写后将作为乾坤子应用启动，主应用注册时AppName需保持一致（放开 VITE_GLOB_QIANKUN_MICRO_APP_NAME 参数表示jeecg-vue3将以乾坤子应用模式启动）
#VITE_GLOB_QIANKUN_MICRO_APP_NAME=jeecg-vue3
# 作为乾坤子应用启动时必填，需与qiankun主应用注册子应用时填写的 entry 保持一致
#VITE_GLOB_QIANKUN_MICRO_APP_ENTRY=//localhost:3001/jeecg-vue3
