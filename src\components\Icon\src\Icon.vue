<template>
  <SvgIcon :size="size" :name="getSvgIcon" v-if="isSvgIcon" :class="[$attrs.class, 'anticon']" :spin="spin" />
  <iconify-icon v-else-if="isIconifyLoaded" :icon="getIconRef" :style="getWrapStyle" :class="[$attrs.class, 'app-iconify anticon', spin && 'app-iconify-spin']" @load="onIconLoad" @error="onIconError"></iconify-icon>
  <span v-else ref="elRef" :class="[$attrs.class, 'app-iconify anticon', spin && 'app-iconify-spin']" :style="getWrapStyle"></span>
</template>
<script lang="ts">
  import type { PropType } from 'vue';
  import { defineComponent, ref, watch, onMounted, nextTick, unref, computed, CSSProperties } from 'vue';
  import SvgIcon from './SvgIcon.vue';

  import { isString } from '/@/utils/is';
  import { propTypes } from '/@/utils/propTypes';

  const SVG_END_WITH_FLAG = '|svg';
  export default defineComponent({
    name: 'Icon',
    components: { SvgIcon },
    props: {
      // icon name
      icon: propTypes.string,
      // icon color
      color: propTypes.string,
      // icon size
      size: {
        type: [String, Number] as PropType<string | number>,
        default: 16,
      },
      spin: propTypes.bool.def(false),
      prefix: propTypes.string.def(''),
    },
    setup(props) {
      const elRef = ref<ElRef>(null);
      const isIconifyLoaded = ref(false);

      const isSvgIcon = computed(() => props.icon?.endsWith(SVG_END_WITH_FLAG));
      const getSvgIcon = computed(() => props.icon.replace(SVG_END_WITH_FLAG, ''));
      const getIconRef = computed(() => `${props.prefix ? props.prefix + ':' : ''}${props.icon}`);

      // 初始化 Iconify
      const initIconify = async () => {
        try {
          // 等待 Web Component 完全初始化
          await nextTick();

          // 检查 iconify-icon 是否可用
          let retries = 0;
          const maxRetries = 20; // 增加重试次数

          while (retries < maxRetries) {
            if (typeof customElements !== 'undefined' && customElements.get('iconify-icon')) {
              // 额外检查：尝试创建一个测试元素
              try {
                const testElement = document.createElement('iconify-icon');
                if (testElement && typeof testElement.setAttribute === 'function') {
                  // 预加载常用图标集
                  await preloadCommonIcons();
                  isIconifyLoaded.value = true;
                  console.log('iconify-icon web component is ready');
                  return;
                }
              } catch (e) {
                console.warn('iconify-icon element creation failed:', e);
              }
            }

            retries++;
            await new Promise(resolve => setTimeout(resolve, 50)); // 减少等待时间，增加检查频率
          }

          console.warn('iconify-icon web component not ready after retries, using fallback');
          await fallbackUpdate();
        } catch (error) {
          console.error('Failed to initialize Iconify:', error);
          // 降级到传统方式
          await fallbackUpdate();
        }
      };

      // 降级更新方法（保持原有逻辑作为备用）
      const fallbackUpdate = async () => {
        if (unref(isSvgIcon)) return;

        const el = unref(elRef);
        if (!el) return;

        await nextTick();
        const icon = unref(getIconRef);
        if (!icon) return;

        // 创建一个简单的占位符，显示图标名称以便调试
        const span = document.createElement('span');
        span.className = 'iconify-fallback';
        span.dataset.icon = icon;
        span.textContent = `[${icon}]`; // 显示图标名称以便调试
        span.style.cssText = 'color: #ff4d4f; font-size: 12px; border: 1px dashed #ff4d4f; padding: 2px; background: #fff2f0;';
        el.textContent = '';
        el.appendChild(span);

        // 在控制台输出调试信息
        console.warn(`Icon fallback for: ${icon}. Iconify may not be loaded or icon not found.`);
      };

      const getWrapStyle = computed((): CSSProperties => {
        const { size, color } = props;
        let fs = size;
        if (isString(size)) {
          fs = parseInt(size, 10);
        }

        return {
          fontSize: `${fs}px`,
          color: color,
          display: 'inline-flex',
        };
      });

      // 监听图标变化
      watch(() => props.icon, () => {
        if (!isIconifyLoaded.value) {
          fallbackUpdate();
        }
      }, { flush: 'post' });



      // 预加载常用图标集
      const preloadCommonIcons = async () => {
        try {
          // 检查全局 iconify-icon 是否可用
          if (typeof window !== 'undefined' && (window as any).IconifyIcon) {
            const IconifyIcon = (window as any).IconifyIcon;

            // 预加载常用图标
            const commonIcons = [
              'ant-design:home',
              'ant-design:sliders-outlined',
              'eva:arrow-ios-downward-outline',
              'ant-design:loading-outlined',
              'ant-design:upload-outlined',
              'ant-design:edit-outlined', // 密码修改
              'ion:power-outline' // 退出系统
            ];

            console.log('Preloading common icons...');
            if (IconifyIcon.loadIcons) {
              await new Promise((resolve) => {
                IconifyIcon.loadIcons(commonIcons, () => {
                  console.log('Common icons preloaded');
                  resolve(true);
                });
              });
            }
          }
        } catch (error) {
          console.warn('Failed to preload icons:', error);
        }
      };

      // 图标加载事件处理
      const onIconLoad = () => {
        console.log('Icon loaded successfully:', unref(getIconRef));
      };

      const onIconError = (error: any) => {
        console.error('Icon load error:', unref(getIconRef), error);
        console.error('Error details:', error);
        // 图标加载失败时，降级处理
        isIconifyLoaded.value = false;
        fallbackUpdate();
      };

      onMounted(initIconify);

      return { elRef, getWrapStyle, isSvgIcon, getSvgIcon, getIconRef, isIconifyLoaded, onIconLoad, onIconError };
    },
  });
</script>
<style lang="less">
  .app-iconify {
    display: inline-block;
    // vertical-align: middle;

    &-spin {
      svg {
        animation: loadingCircle 1s infinite linear;
      }
    }
  }

  span.iconify {
    display: block;
    min-width: 1em;
    min-height: 1em;
    background-color: @iconify-bg-color;
    border-radius: 100%;
  }
</style>
