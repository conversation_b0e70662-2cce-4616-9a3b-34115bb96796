import type { AppRouteRecordRaw, AppRouteModule } from '/@/router/types';

import { PAGE_NOT_FOUND_ROUTE, H5_PAGE_NOT_FOUND_ROUTE, REDIRECT_ROUTE } from '/@/router/routes/basic';

import { PageEnum } from '/@/enums/pageEnum';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

import userCenterRoute from './modules/userCenter';
import dashboardRoute from './modules/dashboard';
import { isMobile } from '/@/utils/index';

const modules = import.meta.glob('./modules/**/*.ts', { eager: true });

const routeModuleList: AppRouteModule[] = [];

// 加入到路由集合中
Object.keys(modules).forEach((key) => {
  const mod = (modules as Recordable)[key].default || {};
  const modList = Array.isArray(mod) ? [...mod] : [mod];
  routeModuleList.push(...modList);
});

const errorPageRoute = !isMobile() ? PAGE_NOT_FOUND_ROUTE : H5_PAGE_NOT_FOUND_ROUTE;
export const asyncRoutes = [errorPageRoute, ...routeModuleList];

export const RootRoute: AppRouteRecordRaw = {
  path: '/',
  name: 'Root',
  redirect: PageEnum.BASE_HOME,
  meta: {
    title: 'Root',
  },
};

// 门户首页
export const Workbenches: AppRouteRecordRaw = {
  path: '/workbenches',
  name: 'workbenches',
  component: () => import('/@/views/dashboard/workbench/index.vue'),
  meta: {
    title: 'Work Benches',
  },
};

// API文档
export const ApiDocument: AppRouteRecordRaw = {
  path: '/api-document',
  name: 'apiDocument',
  component: () => import('/@/views/viewApiDocument/index.vue'),
  meta: {
    title: 'APl Documentation',
  },
};

// 登录
export const LoginRoute: AppRouteRecordRaw = {
  path: '/login',
  name: 'Login',
  component: () => import('/@/views/system/loginmini/MiniLogin.vue'),
  meta: {
    title: t('common.static_state_route_login'),
  },
};

// 选择站点
export const selectSiteRoute: AppRouteRecordRaw = {
  path: '/selectSiteRoute',
  name: 'SelectSiteRoute',
  component: () => import('/@/views/system/siteSelect/index.vue'),
  meta: {
    title: t('common.static_state_route_site_selection'),
  },
};

/**
 * 【通过token直接静默登录】流程办理登录页面 中转跳转
 */
export const TokenLoginRoute: AppRouteRecordRaw = {
  path: '/tokenLogin',
  name: 'TokenLoginRoute',
  component: () => import('/@/views/sys/login/TokenLoginPage.vue'),
  meta: {
    title: t('common.static_state_route_transfer_page'),
    ignoreAuth: true,
  },
};

/**
 * DMS跳转进来没有登录 中转跳转
 */
export const MdsTokenLoginRoute: AppRouteRecordRaw = {
  path: '/transferPage',
  name: 'MdsTokenLoginRoute',
  component: () => import('/@/views/sys/login/MdsTokenLoginPage.vue'),
  meta: {
    title: t('common.static_state_route_transfer_page'),
    ignoreAuth: true,
  },
};

export const basicRoutes = [
  Workbenches,
  userCenterRoute,
  dashboardRoute,
  ApiDocument,
  LoginRoute,
  selectSiteRoute,
  RootRoute,
  REDIRECT_ROUTE,
  !isMobile() ? PAGE_NOT_FOUND_ROUTE : H5_PAGE_NOT_FOUND_ROUTE,
  TokenLoginRoute,
  MdsTokenLoginRoute,
];
