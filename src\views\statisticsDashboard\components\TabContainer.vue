<template>
  <div>
    <a-card :bordered="false" :body-style="{ padding: 0 }" class="bg-white rounded-lg shadow-sm overflow-hidden">
      <a-tabs v-model:activeKey="activeTab" type="card" size="large" @change="handleTabChange" :animated="{ inkBar: true, tabPane: false }">
        <a-tab-pane v-for="tab in tabs" :key="tab.id" :tab="tab.name">
          <!-- Tab内容区域 -->
          <div class="min-h-100" :class="getLayoutClass(tab.layout)">
            <div v-if="loading" class="flex justify-center items-center h-100">
              <a-spin size="large" tip="加载中..." />
            </div>
            <div v-else-if="!tab.groups || tab.groups.length === 0" class="flex justify-center items-center h-100">
              <a-empty description="暂无图表数据">
                <template #image>
                  <Icon icon="ant-design:bar-chart-outlined" style="font-size: 64px; color: #d9d9d9" />
                </template>
                <a-button type="primary" @click="handleRefreshTab">刷新数据</a-button>
              </a-empty>
            </div>
            <div v-else>
              <ChartGroupContainer :data="tab.groups" :gridStyle="gridStyle" />
            </div>
          </div>
        </a-tab-pane>

        <!-- Tab操作区域 -->
        <template #rightExtra>
          <a-space>
            <a-tooltip title="刷新当前Tab">
              <a-button type="text" size="small" :loading="refreshing" @click="handleRefreshTab">
                <Icon icon="ant-design:reload-outlined" />
              </a-button>
            </a-tooltip>

            <a-tooltip title="全屏显示">
              <a-button type="text" size="small" @click="handleFullscreen">
                <Icon icon="ant-design:fullscreen-outlined" />
              </a-button>
            </a-tooltip>

            <a-tooltip title="导出当前Tab">
              <a-button type="text" size="small" @click="handleExport">
                <Icon icon="ant-design:download-outlined" />
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </a-tabs>
    </a-card>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'TabContainer',
  };
</script>
<script setup lang="ts">
  import { ref, computed, watch, nextTick, onMounted } from 'vue';
  import { Icon } from '/@/components/Icon';
  import type { TabConfig } from '../types/statisticDashboard';
  import { useDashboardLayout } from '../hooks/useStatisticDashboard';
  import { message } from 'ant-design-vue';
  import ChartGroupContainer from './ChartGroupContainer.vue';
  import { useChartConfigOptional, useChartActionsOptional } from '../hooks/useChartActions';

  // Props定义
  interface Props {
    /** Tab配置数组 */
    tabs: TabConfig[];
    /** 当前激活的Tab */
    modelValue: string;
    /** 是否加载中 */
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    loading: false,
    chartDataGetter: () => [],
  });

  // Emits定义 - 只保留必要的v-model事件和tab切换事件
  interface Emits {
    'update:modelValue': [value: string];
    'tab-change': [tabId: string, tab: TabConfig];
  }

  const emit = defineEmits<Emits>();

  // 使用上下文系统
  const chartConfig = useChartConfigOptional();
  const chartActions = useChartActionsOptional();

  // 使用布局Hook
  const { getChartContainerStyle, adjustLayout } = useDashboardLayout();

  // 本地状态
  const refreshing = ref(false);

  // 计算属性：当前激活Tab
  const activeTab = computed({
    get: () => props.modelValue,
    set: (value: string) => emit('update:modelValue', value),
  });

  // 计算属性：当前Tab配置
  const currentTab = computed(() => {
    return props.tabs.find((tab) => tab.id === activeTab.value);
  });

  // 计算属性：网格样式
  const gridStyle = computed(() => {
    const baseStyle = getChartContainerStyle.value;
    const layout = currentTab.value?.layout || 'grid';

    switch (layout) {
      case 'flex':
        return {
          display: 'flex',
          flexWrap: 'wrap' as const,
          gap: baseStyle.gap,
          padding: baseStyle.padding,
        };
      case 'custom':
        return {
          display: 'block',
          padding: baseStyle.padding,
        };
      default: // grid
        return baseStyle;
    }
  });

  /**
   * 获取布局样式类
   */
  const getLayoutClass = (layout: string) => {
    switch (layout) {
      case 'flex':
        return 'flex flex-wrap gap-4 p-4';
      case 'custom':
        return 'p-4';
      default: // grid
        return 'grid gap-4 p-4';
    }
  };

  /**
   * 处理Tab切换 - 统一使用上下文系统
   */
  const handleTabChange = (tabId: string) => {
    const tab = props.tabs.find((t) => t.id === tabId);
    if (tab) {
      // 只通过上下文系统处理，避免重复
      chartConfig.setCurrentTab(tabId);

      // 仍然emit给父组件，用于其他必要的处理（如URL更新等）
      emit('tab-change', tabId, tab);

      // 调整布局
      nextTick(() => {
        adjustLayout();
      });
    }
  };

  /**
   * 刷新当前Tab - 实际刷新所有图表
   */
  const handleRefreshTab = async () => {
    if (!currentTab.value) return;

    refreshing.value = true;

    try {
      console.log('刷新Tab:', currentTab.value.id);

      // 刷新当前Tab中的所有图表
      let refreshCount = 0;
      for (const group of currentTab.value.groups || []) {
        for (const chart of group.chartList || []) {
          chartActions.refreshChart(chart.id);
          refreshCount++;
        }
      }

      message.success(`${currentTab.value.name} 已刷新 (${refreshCount}个图表)`);
    } catch (error) {
      console.error('刷新失败:', error);
      message.error('刷新失败');
    } finally {
      setTimeout(() => {
        refreshing.value = false;
      }, 1000);
    }
  };

  /**
   * 全屏显示
   */
  const handleFullscreen = () => {
    if (!currentTab.value) return;

    console.log('全屏显示Tab:', currentTab.value.id);

    // 请求全屏
    const element = document.documentElement;
    if (element.requestFullscreen) {
      element.requestFullscreen();
    }
    message.info('已进入全屏模式');
  };

  /**
   * 导出当前Tab
   */
  const handleExport = () => {
    if (!currentTab.value) return;

    console.log('导出Tab:', currentTab.value.id);
    message.info(`正在导出 ${currentTab.value.name}...`);
  };

  // 组件挂载时初始化布局
  onMounted(() => {
    adjustLayout();
  });

  // 监听Tab变化，预加载数据
  watch(
    () => activeTab.value,
    (newTabId) => {
      const tab = props.tabs.find((t) => t.id === newTabId);
      if (tab && tab.groups) {
        // 这里可以添加预加载逻辑
      }
    }
  );
</script>
