// 线索跟进状态 mock数据

import { ChartConfig } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter } from '../utils';

/**
 * 线索跟进状态配置
 */
export interface FollowUpStatusConfig {
  key: string;
  name: string;
  color: string;
}

/**
 * 线索跟进状态配置
 */
export const followUpStatusConfig: FollowUpStatusConfig[] = [
  { key: 'storeAppointmentInProgress', name: '到店预约中', color: '#5470c6' }, // 蓝色
  { key: 'pendingDistribution', name: '待下发', color: '#91cc75' }, // 绿色
  { key: 'pendingAllocation', name: '待分配', color: '#fac858' }, // 黄色
  { key: 'notFollowedUp', name: '未跟进', color: '#ee6666' }, // 红色
  { key: 'appointedToStore', name: '已预约到店', color: '#73c0de' }, // 青色
  { key: 'arrivedAtStore', name: '已到店适客', color: '#3ba272' }, // 深绿色
  { key: 'depositMade', name: '已下定', color: '#fc8452' }, // 橙色
  { key: 'dealClosed', name: '已成交', color: '#9a60b4' }, // 紫色
  { key: 'defeated', name: '已战败', color: '#ea7ccc' }, // 粉色
  { key: 'followUpInvalid', name: '跟进无效', color: '#5d7092' }, // 深色
  { key: 'recycleDefeated', name: '回收战败', color: '#b87333' }, // 棕色
];

/**
 * 日期线索跟进数据接口
 */
export interface DailyFollowUpData {
  name: string; // 日期名称
  value: number; // 总值
  type: 'period';
  statuses: Record<string, number>; // 各状态数据
  followUpRate: number; // 跟进率百分比
}

/**
 * 线索跟进状态数据（7天数据，对应图片中的日期范围）
 */
export const followUpOnCluesData: DailyFollowUpData[] = [
  {
    name: '2025-07-25',
    value: 387,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 78,
      pendingDistribution: 45,
      pendingAllocation: 52,
      notFollowedUp: 85,
      appointedToStore: 32,
      arrivedAtStore: 28,
      depositMade: 24,
      dealClosed: 20,
      defeated: 12,
      followUpInvalid: 8,
      recycleDefeated: 3,
    },
    followUpRate: 55.0, // (已跟进相关状态的总和 / 总数) * 100
  },
  {
    name: '2025-07-26',
    value: 432,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 94,
      pendingDistribution: 74,
      pendingAllocation: 54,
      notFollowedUp: 55,
      appointedToStore: 74,
      arrivedAtStore: 34,
      depositMade: 24,
      dealClosed: 20,
      defeated: 15,
      followUpInvalid: 10,
      recycleDefeated: 4,
    },
    followUpRate: 58.0,
  },
  {
    name: '2025-07-27',
    value: 398,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 68,
      pendingDistribution: 82,
      pendingAllocation: 48,
      notFollowedUp: 65,
      appointedToStore: 52,
      arrivedAtStore: 30,
      depositMade: 22,
      dealClosed: 18,
      defeated: 10,
      followUpInvalid: 7,
      recycleDefeated: 2,
    },
    followUpRate: 52.0,
  },
  {
    name: '2025-07-28',
    value: 457,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 94,
      pendingDistribution: 74,
      pendingAllocation: 54,
      notFollowedUp: 55,
      appointedToStore: 74,
      arrivedAtStore: 34,
      depositMade: 24,
      dealClosed: 20,
      defeated: 18,
      followUpInvalid: 12,
      recycleDefeated: 6,
    },
    followUpRate: 60.0,
  },
  {
    name: '2025-07-29',
    value: 246,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 58,
      pendingDistribution: 32,
      pendingAllocation: 28,
      notFollowedUp: 35,
      appointedToStore: 38,
      arrivedAtStore: 22,
      depositMade: 15,
      dealClosed: 12,
      defeated: 6,
      followUpInvalid: 4,
      recycleDefeated: 2,
    },
    followUpRate: 62.0,
  },
  {
    name: '2025-07-30',
    value: 523,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 125,
      pendingDistribution: 95,
      pendingAllocation: 68,
      notFollowedUp: 45,
      appointedToStore: 85,
      arrivedAtStore: 42,
      depositMade: 28,
      dealClosed: 22,
      defeated: 20,
      followUpInvalid: 15,
      recycleDefeated: 8,
    },
    followUpRate: 59.0,
  },
  {
    name: '2025-07-31',
    value: 456,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 98,
      pendingDistribution: 78,
      pendingAllocation: 58,
      notFollowedUp: 48,
      appointedToStore: 72,
      arrivedAtStore: 38,
      depositMade: 26,
      dealClosed: 20,
      defeated: 14,
      followUpInvalid: 10,
      recycleDefeated: 4,
    },
    followUpRate: 58.0,
  },
];

/**
 * 生成图表数据项（包含百分比）
 */
const generateChartDataItem = (name: string, value: number, percent: string, additionalProps: Record<string, any> = {}): any => {
  return {
    name,
    value: Number(value) || 0,
    percent: parseFloat(percent),
    ...additionalProps,
  };
};

/**
 * 线索跟进状态tooltip格式化函数
 */
const formatFollowUpTooltip = createTooltipFormatter({
  showPercentage: true,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: '跟进率' },
  extraInfoProvider: (axisValue: string) => {
    // 查找对应日期的数据来获取跟进率和汇总信息
    const dayData = followUpOnCluesData.find((item) => item.name === axisValue);
    if (!dayData) return '';

    // 计算跟进中的总数（排除未跟进的状态）
    const followingUpCount = Object.keys(dayData.statuses)
      .filter((key) => key !== 'notFollowedUp')
      .reduce((sum, key) => sum + (dayData.statuses[key] || 0), 0);

    const followingUpPercentage = dayData.value > 0 ? ((followingUpCount / dayData.value) * 100).toFixed(1) : '0.0';

    return [`总数：${dayData.value}`, `跟进中：${followingUpCount} (${followingUpPercentage}%)`, `跟进率：${dayData.followUpRate}%`].join('<br/>');
  },
});

/**
 * 生成线索跟进状态图表配置
 */
export const generateFollowUpOnCluesChartConfig = (): ChartConfig => {
  const data = followUpOnCluesData;

  // 生成堆叠系列配置（包含所有状态）
  const stackSeries = followUpStatusConfig.map((statusItem) => ({
    name: statusItem.name,
    type: 'bar',
    stack: 'followUp', // 堆叠配置
    data: data.map((item) => {
      const value = item.statuses[statusItem.key] || 0;
      const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
      return generateChartDataItem(item.name, value, percent, {
        statusKey: statusItem.key,
      });
    }),
    itemStyle: {
      color: statusItem.color,
    },
    label: {
      show: false, // 在堆叠图中通常不显示标签，避免重叠
    },
  }));

  // 生成跟进率趋势线系列配置
  const followUpRateSeries = {
    name: '跟进率',
    type: 'line',
    yAxisIndex: 1, // 使用右侧Y轴
    data: data.map((item, index) => ({
      name: item.name,
      value: item.followUpRate,
      // 只在头尾显示标签
      label: {
        show: index === 0 || index === data.length - 1,
        position: 'top',
        formatter: '{c}%',
        fontSize: 12,
        color: '#8B0000', // 深红色标签
      },
    })),
    itemStyle: {
      color: '#8B0000', // 深红色数据点
    },
    lineStyle: {
      color: '#8B0000', // 深红色线条
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  // 合并所有系列
  const series = [...stackSeries, followUpRateSeries];

  return {
    id: 'followUpOnClues',
    type: 'bar',
    title: '线索跟进状态',
    dataSource: 'followUpOnClues',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: [...followUpStatusConfig.map((config) => config.color), '#8B0000'], // 包含所有状态和深红色趋势线
      grid: {
        left: '4%',
        right: '8%', // 为右侧Y轴留出更多空间
        bottom: '15%', // 为底部legend留出更多空间
        containLabel: true,
      },
      legend: {
        data: [...followUpStatusConfig.map((config) => config.name), '跟进率'], // 包含所有状态和跟进率
        bottom: '5%', // legend放在底部
        left: 'center',
        type: 'scroll', // 如果legend项太多，支持滚动
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '线索数量',
          position: 'left',
        },
        {
          type: 'value',
          name: '跟进率(%)',
          position: 'right',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%',
          },
        },
      ],
      series: series as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatFollowUpTooltip, // 使用专用的tooltip格式化函数
      },
    },
    // 尺寸配置支持多种格式：
    // - 数字: 550 → '550px'
    // - 百分比: '100%' → '100%'
    // - 视口单位: '50vh', '80vw' → '50vh', '80vw'
    // - CSS单位: '300px', '20rem' → '300px', '20rem'
    size: { width: '100%', height: 550 },
    position: { x: 0, y: 0 },
  };
};

/**
 * 首次跟进时长区间配置
 */
export interface FollowUpTimeRangeConfig {
  key: string;
  name: string;
  color: string;
}

/**
 * 首次跟进时长区间配置
 */
export const followUpTimeRangeConfig: FollowUpTimeRangeConfig[] = [
  { key: '0-4h', name: '0-4小时', color: '#5470c6' }, // 蓝色
  { key: '4-8h', name: '4-8小时', color: '#5470c6' },
  { key: '8-12h', name: '8-12小时', color: '#5470c6' },
  { key: '12-16h', name: '12-16小时', color: '#5470c6' },
  { key: '16-20h', name: '16-20小时', color: '#5470c6' },
  { key: '20-24h', name: '20-24小时', color: '#5470c6' },
];

/**
 * 首次跟进时长分析数据接口
 */
export interface FirstFollowUpTimeAnalysisData {
  name: string; // 时长区间名称
  value: number; // 线索数量
  type: 'timeRange';
  trendRate: number; // 趋势率百分比
  percentage: number; // 占比百分比
}

/**
 * 首次跟进时长分析数据（根据图片数据）
 */
export const firstFollowUpTimeAnalysisData: FirstFollowUpTimeAnalysisData[] = [
  {
    name: '0-4小时',
    value: 120,
    type: 'timeRange',
    trendRate: 33.8,
    percentage: 33.8,
  },
  {
    name: '4-8小时',
    value: 85,
    type: 'timeRange',
    trendRate: 23.9,
    percentage: 23.9,
  },
  {
    name: '8-12小时',
    value: 60,
    type: 'timeRange',
    trendRate: 16.9,
    percentage: 16.9,
  },
  {
    name: '12-16小时',
    value: 45,
    type: 'timeRange',
    trendRate: 12.7,
    percentage: 12.7,
  },
  {
    name: '16-20小时',
    value: 30,
    type: 'timeRange',
    trendRate: 8.5,
    percentage: 8.5,
  },
  {
    name: '20-24小时',
    value: 15,
    type: 'timeRange',
    trendRate: 4.2,
    percentage: 4.2,
  },
];

/**
 * 首次跟进时长分析tooltip格式化函数
 */
const formatFirstFollowUpTimeTooltip = createTooltipFormatter({
  showPercentage: false,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: '趋势率' },
  extraInfoProvider: (axisValue: string) => {
    // 查找对应时长区间的数据来获取趋势率
    const timeData = firstFollowUpTimeAnalysisData.find((item) => item.name === axisValue);
    const trendRate = timeData?.trendRate || 0;
    return `趋势率: ${trendRate}%`;
  },
});

/**
 * 生成首次跟进时长分析图表配置
 */
export const generateFirstFollowUpTimeAnalysisChartConfig = (): ChartConfig => {
  const data = firstFollowUpTimeAnalysisData;

  // 生成柱状图系列配置
  const barSeries = {
    name: '线索数量',
    type: 'bar',
    data: data.map((item) => {
      return generateChartDataItem(item.name, item.value, item.percentage.toString(), {
        timeRangeKey: item.name,
        trendRate: item.trendRate,
      });
    }),
    itemStyle: {
      color: '#5470c6', // 蓝色
    },
    label: {
      show: true,
      position: 'top',
      formatter: (params: any) => {
        // 显示数值和百分比
        return `${params.data.value} (${params.data.percent}%)`;
      },
      fontSize: 12,
      color: '#333',
    },
    barWidth: '60%', // 设置柱子宽度
  };

  // 生成趋势率折线系列配置
  const trendLineSeries = {
    name: '趋势率',
    type: 'line',
    yAxisIndex: 1, // 使用右侧Y轴
    data: data.map((item) => ({
      name: item.name,
      value: item.trendRate,
      // 在所有点显示标签
      label: {
        show: true,
        position: 'top',
        formatter: '{c}%',
        fontSize: 12,
        color: '#ff4d4f', // 红色标签
      },
    })),
    itemStyle: {
      color: '#ff4d4f', // 红色数据点
    },
    lineStyle: {
      color: '#ff4d4f', // 红色线条
      width: 2,
    },
    symbol: 'circle',
    // symbolSize: 6,
  };

  return {
    id: 'firstFollowUpTimeAnalysis',
    type: 'bar',
    title: '首次跟进时长分析',
    dataSource: 'firstFollowUpTimeAnalysis',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: ['#5470c6', '#ff4d4f'], // 蓝色柱状图和红色折线
      grid: {
        left: '8%',
        right: '8%', // 为右侧Y轴留出空间
        bottom: '15%',
        top: '15%',
        containLabel: true,
      },
      legend: {
        data: ['线索数量', '趋势率'],
        bottom: '5%',
        left: 'center',
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 0, // 不旋转
          fontSize: 12,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '线索数量',
          position: 'left',
          axisLabel: {
            formatter: '{value}',
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
          },
        },
        {
          type: 'value',
          name: '趋势率(%)',
          position: 'right',
          min: 0,
          max: 40, // 根据图片数据设置最大值
          axisLabel: {
            formatter: '{value}%',
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
          },
        },
      ],
      series: [barSeries, trendLineSeries] as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatFirstFollowUpTimeTooltip,
      },
    },
    // 尺寸配置支持多种格式：
    // - 数字: 550 → '550px'
    // - 百分比: '100%' → '100%'
    // - 视口单位: '50vh', '80vw' → '50vh', '80vw'
    // - CSS单位: '300px', '20rem' → '300px', '20rem'
    size: { width: '100%', height: 550 },
    position: { x: 0, y: 0 },
  };
};

/**
 * 首次响应超时数据接口
 */
export interface FirstResponseTimeoutData {
  name: string; // 日期
  value: number; // 总数（用于兼容ChartDataItem）
  type: 'period';
  timeoutCount: number; // 超过72小时的线索数
  withinTimeCount: number; // 72小时内的线索数
  timeoutRate: number; // 超时率百分比
}

/**
 * 首次响应超时数据（7天数据，根据图片趋势）
 */
export const firstResponseTimeoutData: FirstResponseTimeoutData[] = [
  {
    name: '2025-1-1',
    value: 230, // 总数
    type: 'period',
    timeoutCount: 42, // 超过72小时
    withinTimeCount: 188, // 72小时内
    timeoutRate: 18.3, // 超时率
  },
  {
    name: '2025-1-2',
    value: 235,
    type: 'period',
    timeoutCount: 45,
    withinTimeCount: 190,
    timeoutRate: 19.1,
  },
  {
    name: '2025-1-3',
    value: 240,
    type: 'period',
    timeoutCount: 48,
    withinTimeCount: 192,
    timeoutRate: 20.0,
  },
  {
    name: '2025-1-4',
    value: 245,
    type: 'period',
    timeoutCount: 50,
    withinTimeCount: 195,
    timeoutRate: 20.4,
  },
  {
    name: '2025-1-5',
    value: 250,
    type: 'period',
    timeoutCount: 52,
    withinTimeCount: 198,
    timeoutRate: 20.8,
  },
  {
    name: '2025-1-6',
    value: 260,
    type: 'period',
    timeoutCount: 58,
    withinTimeCount: 202,
    timeoutRate: 22.3,
  },
  {
    name: '2025-1-7',
    value: 265,
    type: 'period',
    timeoutCount: 62,
    withinTimeCount: 203,
    timeoutRate: 23.4,
  },
];

/**
 * 生成首次响应超时图表配置
 */
function generateFirstResponseTimeoutChartConfig(): ChartConfig {
  // 专用tooltip格式化函数
  const formatFirstResponseTimeoutTooltip = (params: any) => {
    if (!params || !Array.isArray(params) || params.length === 0) return '';

    const axisValue = params[0].axisValue;
    const timeoutData = firstResponseTimeoutData.find((item) => item.name === axisValue);

    if (!timeoutData) return '';

    return [
      `<div style="margin-bottom: 8px; font-weight: bold;">${axisValue}</div>`,
      `超时线索：${timeoutData.timeoutCount}`,
      `总线索数：${timeoutData.value}`,
      `超时率：${timeoutData.timeoutRate}%`,
    ].join('<br/>');
  };

  // 面积图系列 - 超过72小时的线索
  const timeoutAreaSeries = {
    name: '首次响应超过72小时的线索',
    type: 'line' as const,
    stack: 'total',
    smooth: true,
    areaStyle: {
      color: {
        type: 'linear' as const,
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: 'rgba(255, 99, 132, 0.8)' },
          { offset: 1, color: 'rgba(255, 99, 132, 0.3)' },
        ],
      },
    },
    lineStyle: {
      color: '#ff6384',
      width: 2,
    },
    itemStyle: {
      color: '#ff6384',
    },
    symbol: 'circle',
    symbolSize: 6,
    data: firstResponseTimeoutData.map((item) => item.timeoutCount),
  };

  // 面积图系列 - 72小时内的线索
  const withinTimeAreaSeries = {
    name: '首次响应72小时内的线索',
    type: 'line' as const,
    stack: 'total',
    smooth: true,
    areaStyle: {
      color: {
        type: 'linear' as const,
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: 'rgba(75, 192, 192, 0.8)' },
          { offset: 1, color: 'rgba(75, 192, 192, 0.3)' },
        ],
      },
    },
    lineStyle: {
      color: '#4bc0c0',
      width: 2,
    },
    itemStyle: {
      color: '#4bc0c0',
    },
    symbol: 'circle',
    symbolSize: 6,
    data: firstResponseTimeoutData.map((item) => item.withinTimeCount),
  };

  return {
    id: 'firstResponseTimeout',
    type: 'line',
    title: '首次响应超时',
    dataSource: 'firstResponseTimeout',
    position: { x: 0, y: 0 },
    size: { width: '100%', height: 550 },
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: formatFirstResponseTimeoutTooltip,
      },
      legend: {
        data: ['首次响应超过72小时的线索', '首次响应72小时内的线索'],
        bottom: '5%',
        textStyle: {
          fontSize: 12,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: firstResponseTimeoutData.map((item) => item.name),
          axisLabel: {
            interval: 0,
            rotate: 30,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '线索数量',
          nameTextStyle: {
            color: '#666',
            fontSize: 12,
          },
          axisLabel: {
            formatter: '{value}',
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0',
            },
          },
        },
      ],
      series: [timeoutAreaSeries, withinTimeAreaSeries],
    },
  };
}

/** 线索跟进状态图表配置 */
export const followUpOnCluesChartConfigs: ChartConfig[] = [
  generateFollowUpOnCluesChartConfig(),
  generateFirstFollowUpTimeAnalysisChartConfig(),
  generateFirstResponseTimeoutChartConfig(),
];
