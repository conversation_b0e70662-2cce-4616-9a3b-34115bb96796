# 构建性能监控快速上手指南

## 🚀 快速开始

### 1. 基础监控
```bash
# 正常构建，自动显示监控信息
pnpm build
```

### 2. 详细分析
```bash
# 运行完整的构建性能分析
pnpm run build:analyze
```

### 3. 快速构建
```bash
# 使用优化的内存配置
pnpm run build:fast
```

## 📊 监控输出解读

### 构建时间分析
```
⏱️ 各阶段耗时分析:
┌─────────┬──────────────────┬────────┬────────┐
│ (index) │       阶段       │  耗时  │  占比  │
├─────────┼──────────────────┼────────┼────────┤
│    0    │ 'Vue SFC 编译'   │ '21720ms'│ '93.8%'│ ← 主要瓶颈
│    1    │   '构建初始化'   │ '571ms' │ '2.5%' │
│    2    │   'CSS 处理'     │ '377ms' │ '1.6%' │
└─────────┴──────────────────┴────────┴────────┘
```

**关键指标**：
- **占比 >50%** 的阶段是主要瓶颈
- **Vue SFC 编译** 通常是最耗时的阶段
- **依赖解析** 时间过长说明需要优化 optimizeDeps

### 依赖加载分析
```
📦 依赖加载时间分析 (Top 10):
┌─────────┬──────────────────┬──────────┬──────────┐
│ (index) │     依赖名称     │ 加载时间 │ 代码大小 │
├─────────┼──────────────────┼──────────┼──────────┤
│    0    │ 'ant-design-vue' │ '2500ms' │ '850.2KB'│ ← 最慢依赖
│    1    │    'echarts'     │ '1800ms' │ '1.2MB'  │
└─────────┴──────────────────┴──────────┴──────────┘
```

**优化建议**：
- **>1000ms** 的依赖考虑添加到 `optimizeDeps.include`
- **大型依赖** 考虑 CDN 化或按需加载

### 系统资源分析
```
💻 系统资源使用分析:
峰值内存使用: 1,917.97 MB ← 关注内存使用
内存增长: +1,822.14 MB
```

**性能建议**：
- **>1GB** 内存使用：检查是否有内存泄漏
- **>2GB** 内存使用：增加 Node.js 内存限制

### 构建产物分析
```
📦 主要文件 (Top 10):
┌─────────┬──────────────────┬──────────┬──────────┬────────┐
│ (index) │     文件名       │ 原始大小 │ Gzip大小 │ 压缩率 │
├─────────┼──────────────────┼──────────┼──────────┼────────┤
│    0    │ 'antd-vue-vendor'│ '1.37MB' │ '420KB'  │ '69.5%'│
│    1    │ 'index-CC8Hex9Y' │ '589KB'  │ '185KB'  │ '68.6%'│
└─────────┴──────────────────┴──────────┴──────────┴────────┘
```

**优化建议**：
- **>1MB** 文件：考虑进一步拆分
- **压缩率 <60%**：检查是否包含已压缩内容

## 🎯 常见问题和解决方案

### Q1: 构建时间过长 (>30秒)
**分析步骤**：
1. 查看 Vue SFC 编译时间占比
2. 检查依赖加载时间
3. 分析系统资源使用

**解决方案**：
- Vue SFC 编译慢：拆分大型组件
- 依赖加载慢：优化 optimizeDeps 配置
- 内存不足：增加 Node.js 内存限制

### Q2: 内存使用过高 (>2GB)
**检查项**：
```bash
# 检查 Node.js 内存限制
node -e "console.log(v8.getHeapStatistics())"

# 使用更大的内存限制
NODE_OPTIONS="--max-old-space-size=8192" pnpm build
```

### Q3: 构建产物过大
**分析方法**：
1. 查看最大的文件
2. 检查压缩率
3. 分析模块数量

**优化策略**：
- 进一步代码分割
- 移除未使用的依赖
- 启用 tree-shaking

## 🔧 高级使用

### 依赖时间 API
开发环境下访问：`http://localhost:3101/api/deps-timing`

返回 JSON 格式的依赖数据：
```json
{
  "dependencies": [
    {
      "name": "ant-design-vue",
      "loadTime": 2500,
      "size": 870400
    }
  ],
  "totalDeps": 45,
  "totalLoadTime": 15600
}
```

### 自定义监控
在插件中添加自定义监控：
```typescript
// 在 build-timer.ts 中添加
addTiming('自定义阶段');
```

### 历史数据对比
```bash
# 保存当前构建数据
cp build-results.json build-results-$(date +%Y%m%d).json

# 对比历史数据
node scripts/compare-builds.js build-results-20250717.json build-results.json
```

## 📈 持续优化流程

### 1. 建立基线
```bash
# 首次运行，建立性能基线
pnpm run build:analyze
cp build-results.json baseline-build-results.json
```

### 2. 定期监控
```bash
# 每周运行一次
pnpm run build:analyze

# 对比基线
node scripts/compare-with-baseline.js
```

### 3. 优化验证
```bash
# 优化前
pnpm run build:analyze
cp build-results.json before-optimization.json

# 实施优化...

# 优化后
pnpm run build:analyze
cp build-results.json after-optimization.json

# 对比效果
node scripts/compare-builds.js before-optimization.json after-optimization.json
```

## 🚨 告警阈值

### 构建时间告警
- **警告**: >30秒
- **严重**: >60秒

### 内存使用告警
- **警告**: >1.5GB
- **严重**: >3GB

### 文件大小告警
- **警告**: 单文件 >2MB
- **严重**: 单文件 >5MB

## 📞 技术支持

### 常用命令
```bash
# 清理构建缓存
rm -rf node_modules/.vite dist .tsbuildinfo

# 强制重新预构建依赖
rm -rf node_modules/.vite && pnpm build

# 查看详细构建日志
VITE_DEBUG=vite:* pnpm build
```

### 问题排查
1. 查看 `build-analysis.log` 文件
2. 检查控制台错误信息
3. 分析系统资源使用情况
4. 对比历史构建数据

---

**文档版本**: v1.0  
**最后更新**: 2025-07-17  
**维护人员**: 开发团队
