# 统计看板筛选组件使用说明

## 概述

统计看板筛选组件支持根据统计周期动态调整时间范围选择器，并提供统一的输出格式和显示格式配置。

## 功能特性

### 1. 动态时间范围选择器

根据选择的统计周期，时间范围选择器会自动切换到对应的模式：

- **天（day）**：日期选择器，默认近7天
- **周（week）**：周选择器，默认近4周  
- **月（month）**：月选择器，默认近6个月
- **季度（quarter）**：季度选择器，默认近4个季度
- **年（year）**：年选择器，默认近5年

### 2. 统一输出格式配置

为了方便后端处理，组件支持配置统一的输出格式：

#### 可用格式类型：

```typescript
OUTPUT_FORMAT_CONFIG = {
  DATETIME: 'YYYY-MM-DD HH:mm:ss',  // 日期时间格式
  DATE: 'YYYY-MM-DD',               // 日期格式
  TIMESTAMP: 'timestamp',           // 时间戳格式
}
```

### 3. 统一显示格式配置

为了符合产品要求，组件支持配置统一的显示格式：

#### 可用显示模式：

```typescript
DISPLAY_FORMAT_CONFIG = {
  UNIFIED: 'YYYY-MM-DD',    // 统一显示格式（所有周期都显示为YYYY-MM-DD）
  ADAPTIVE: 'adaptive',     // 自适应显示格式（根据统计周期变化）
}
```

#### 配置方法：

```typescript
import { useFilters } from '../hooks/useFilters';

const { 
  setOutputFormat, 
  setDisplayFormatMode,
  OUTPUT_FORMAT_CONFIG,
  DISPLAY_FORMAT_CONFIG 
} = useFilters();

// 设置输出格式
setOutputFormat(OUTPUT_FORMAT_CONFIG.DATETIME);

// 设置显示格式模式
setDisplayFormatMode(DISPLAY_FORMAT_CONFIG.UNIFIED);   // 统一显示
setDisplayFormatMode(DISPLAY_FORMAT_CONFIG.ADAPTIVE);  // 自适应显示
```

### 4. 输出效果对比

#### 自适应显示模式（ADAPTIVE）：
- **天模式**：显示 `2025-07-30` ~ `2025-08-05`
- **周模式**：显示 `2025-31周` ~ `2025-32周`
- **月模式**：显示 `2025-07` ~ `2025-08`

#### 统一显示模式（UNIFIED）：
- **所有模式**：都显示 `2025-07-30` ~ `2025-08-05`

#### 输出数据（后端接收）：
```javascript
// 使用 DATETIME 格式
{
  statPeriod: 'week',
  periodRange: ['2025-07-30 00:00:00', '2025-08-05 23:59:59'], // 统一格式
  regions: ['North', 'South']
}

// 使用 TIMESTAMP 格式
{
  statPeriod: 'month',
  periodRange: ['1722297600', '1722902399'], // 时间戳格式
  regions: []
}
```

## 使用方法

### 在组件中配置格式

```vue
<script setup lang="ts">
import { useFilters } from '../hooks/useFilters';

const { 
  formSchemas, 
  setOutputFormat, 
  setDisplayFormatMode,
  OUTPUT_FORMAT_CONFIG,
  DISPLAY_FORMAT_CONFIG 
} = useFilters();

// 根据产品需求配置显示格式
setDisplayFormatMode(DISPLAY_FORMAT_CONFIG.UNIFIED); // 统一显示为 YYYY-MM-DD

// 根据后端需求配置输出格式
setOutputFormat(OUTPUT_FORMAT_CONFIG.TIMESTAMP); // 输出时间戳格式
</script>
```

## 配置建议

### 显示格式选择：
- **产品要求统一**：使用 `UNIFIED` 模式，所有周期都显示为 `YYYY-MM-DD`
- **用户体验优先**：使用 `ADAPTIVE` 模式，根据周期显示最合适的格式

### 输出格式选择：
- **通用场景**：使用 `DATETIME` 格式（`YYYY-MM-DD HH:mm:ss`）
- **只需日期**：使用 `DATE` 格式（`YYYY-MM-DD`）
- **性能优先**：使用 `TIMESTAMP` 格式（数字时间戳）

## 技术实现

### 对象映射优化

使用对象映射替代switch语句，提高代码可维护性：

```typescript
const STAT_PERIOD_CONFIG: Record<string, StatPeriodConfig> = {
  day: {
    displayFormat: 'YYYY-MM-DD',
    pickerType: undefined,
    defaultRange: { subtract: 6, unit: 'day' },
  },
  // ... 其他配置
};
```

### 动态格式选择

```typescript
const getDisplayFormat = () => {
  if (displayFormatMode.value === DISPLAY_FORMAT_CONFIG.UNIFIED) {
    return DISPLAY_FORMAT_CONFIG.UNIFIED; // 统一使用 YYYY-MM-DD
  }
  return config.displayFormat; // 自适应格式
};
```

这种设计既满足了产品的统一性要求，又保持了技术实现的灵活性。
