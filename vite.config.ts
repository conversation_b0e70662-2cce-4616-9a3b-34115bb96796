import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { createProxy } from './build/vite/proxy';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();

  const env = loadEnv(mode, root);

  // The boolean type read by loadEnv is a string. This function can be converted to boolean type
  const viteEnv = wrapperEnv(env);

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv;

  const isBuild = command === 'build';

  const serverOptions: Recordable = {};

  // ----- [begin] 【JEECG作为乾坤子应用】 -----
  const { VITE_GLOB_QIANKUN_MICRO_APP_NAME, VITE_GLOB_QIANKUN_MICRO_APP_ENTRY } = viteEnv;
  const isQiankunMicro = VITE_GLOB_QIANKUN_MICRO_APP_NAME != null && VITE_GLOB_QIANKUN_MICRO_APP_NAME !== '';
  if (isQiankunMicro && !isBuild) {
    serverOptions.cors = true;
    serverOptions.origin = VITE_GLOB_QIANKUN_MICRO_APP_ENTRY!.split('/').slice(0, 3).join('/');
  }
  // ----- [end] 【JEECG作为乾坤子应用】 -----

  return {
    base: isQiankunMicro ? VITE_GLOB_QIANKUN_MICRO_APP_ENTRY : VITE_PUBLIC_PATH,
    root,
    assetsInclude: ['**/*.xlsx'],
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        // /@/xxxx => src/xxxx
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        // /#/xxxx => types/xxxx
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      // Listening on all local IPs
      host: true,
      // @ts-ignore
      https: false,
      port: VITE_PORT,
      allowedHosts: [],
      // Load proxy configuration from .env
      proxy: createProxy(VITE_PROXY),
      open: true,
      // 合并 server 配置
      ...serverOptions,
    },
    build: {
      minify: 'esbuild', // 使用 esbuild 内置压缩
      target: 'es2020', // 提升到更现代的目标
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      // 排查线上问题时候开启
      sourcemap: false,
      rollupOptions: {
        // 关闭除屑优化，防止删除重要代码，导致打包后功能出现异常
        treeshake: false,
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          // manualChunks配置 (依赖包从大到小排列)
          manualChunks: {
            // Vue生态核心
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // UI框架
            'antd-vue-vendor': ['ant-design-vue', '@ant-design/colors'],
            // 图标库
            'antd-icons-vendor': ['@ant-design/icons-vue'],
            // 表格组件
            'vxe-table-vendor': ['vxe-table', 'vxe-table-plugin-antd', 'xe-utils'],
            // 富文本编辑器
            'editor-vendor': ['tinymce', '@tinymce/tinymce-vue'],
            // 工具库（移除 lodash-es，让 Vite 自动处理）
            'utils-vendor': ['dayjs', 'crypto-js', 'axios'],
            // 地区数据
            'area-data-vendor': ['@vant/area-data'],
            // 表情包
            'emoji-vendor': ['emoji-mart-vue-fast'],
            // Excel处理
            'excel-vendor': ['xlsx'],
            // 微前端
            'micro-vendor': ['qiankun'],
            // 移动端UI框架
            'vant-vendor': ['vant'],
            // 国际化
            'i18n-vendor': ['vue-i18n'],
          },
        },
      },
      // 关闭brotliSize显示可以稍微减少打包时间
      reportCompressedSize: false,
      // 提高超大静态资源警告大小
      chunkSizeWarningLimit: 2000,
    },
    esbuild: {
      // 启用更快的 esbuild 配置
      target: 'es2020',
      drop: isBuild ? ['console', 'debugger'] : [],
      // 启用更多优化
      legalComments: 'none',
      minifyIdentifiers: isBuild,
      minifySyntax: isBuild,
      minifyWhitespace: isBuild,
      treeShaking: true,
    },
    define: {
      // setting vue-i18-next
      // Suppress warning
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },

    // The vite plugin used by the project. The quantity is large, so it is separately extracted and managed
    // 预加载构建配置（首屏性能)
    plugins: [
      ...createVitePlugins(viteEnv, isBuild, isQiankunMicro),
      // 处理Vue文件的sourcemap冲突问题 排查线上问题的时候开启
      // {
      //   name: 'handle-vue-sourcemap-conflicts',
      //   enforce: 'pre',
      //   transform(code, id) {
      //     // 仅处理Vue文件
      //     if (id.endsWith('.vue')) {
      //       // 生成一个基于文件路径的唯一标识符
      //       const normalizedPath = id.replace(/\\/g, '/');
      //       const uniqueId = normalizedPath.replace(/^.*\/src\//, 'src/');
      //       return {
      //         code,
      //         // 提供一个自定义的sourcemap，确保源文件路径统一
      //         map: {
      //           version: 3,
      //           sources: [uniqueId],
      //           sourcesContent: [code],
      //           names: [],
      //           mappings: 'AAAA', // 基本映射
      //           file: uniqueId,
      //         },
      //       };
      //     }
      //   },
      // },
    ],
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
      },
      exclude: [
        //升级vite4后，需要排除online依赖
        '@jeecg/online',
      ],
    },
  };
};
