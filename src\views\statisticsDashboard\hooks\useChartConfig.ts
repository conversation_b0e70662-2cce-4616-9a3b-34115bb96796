/**
 * 图表配置管理Hook
 * 负责图表配置的动态生成、主题管理、样式定制等
 */

import { reactive } from 'vue';
import type { EChartsOption } from 'echarts';
import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';

/**
 * 默认图表配置
 */
const defaultChartConfig = {
  colors: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
  backgroundColor: '#ffffff',
  textColor: '#333333',
  gridLineColor: '#e0e0e0',
};

/**
 * 图表配置Hook
 */
export function useChartConfig() {
  // 全局图表配置
  const globalConfig = reactive({
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut' as const,
    responsive: true,
  });

  /**
   * 生成基础图表配置
   */
  const generateBaseOption = (config: ChartConfig): EChartsOption => {
    return {
      animation: globalConfig.animation,
      animationDuration: globalConfig.animationDuration,
      animationEasing: globalConfig.animationEasing,
      color: defaultChartConfig.colors,
      backgroundColor: defaultChartConfig.backgroundColor,
      textStyle: {
        color: defaultChartConfig.textColor,
      },
      title: {
        text: config.title,
        left: 'center',
        textStyle: {
          color: defaultChartConfig.textColor,
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: 'transparent',
        textStyle: {
          color: '#ffffff',
        },
        formatter: (params: any) => {
          const hasChildren = params.data?.children && params.data.children.length > 0;
          const drillHint = hasChildren ? '<br/><span style="color: #40a9ff;">💡 点击可查看详细数据</span>' : '';
          return `${params.name}: ${params.value}${drillHint}`;
        },
      },
      legend: {
        textStyle: {
          color: defaultChartConfig.textColor,
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
        borderColor: defaultChartConfig.gridLineColor,
      },
      // 合并用户自定义配置
      ...config.options,
    };
  };

  /**
   * 生成柱状图配置
   */
  const generateBarOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);

    // 如果配置中已经有series，优先使用配置的series
    if (config.options?.series && Array.isArray(config.options.series)) {
      return baseOption; // baseOption已经通过...config.options合并了series
    }

    // 如果配置中已经有xAxis数据，优先使用配置的xAxis
    const xAxisData = (config.options?.xAxis as any)?.data || data.map((item) => item.name);

    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        ...config.options?.xAxis, // 合并用户自定义的xAxis配置
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        splitLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        ...config.options?.yAxis, // 合并用户自定义的yAxis配置
      },
      series: [
        {
          type: 'bar',
          data: data.map((item) => ({
            // 保留原始数据的所有属性，特别是children用于数据下探
            ...item,
            itemStyle: {
              borderRadius: [4, 4, 0, 0],
            },
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
    };
  };

  /**
   * 生成饼图配置
   */
  const generatePieOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);
    const total = data.reduce((sum: number, item: any) => sum + item.value, 0);

    return {
      ...baseOption,
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: data.map((item: any) => ({
            name: item.name,
            value: item.value,
            ...item,
          })),
          label: {
            show: true,
            position: 'outside',
            formatter: (params: any) => {
              const percentage = ((params.value / total) * 100).toFixed(1);
              return `{name|${params.name}}: {percent|${percentage}%}`;
            },
            rich: {
              name: {
                fontSize: 12,
                fontWeight: 'bold',
                color: defaultChartConfig.textColor,
                lineHeight: 16,
              },
              percent: {
                fontSize: 13,
                fontWeight: 'bold',
                color: '#1890ff',
                lineHeight: 16,
              },
            },
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            smooth: true,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
            label: {
              fontSize: 14,
            },
          },
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2,
          },
        },
      ],
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: 'center',
          style: {
            text: `总计\n${total}`,
            fill: defaultChartConfig.textColor,
            fontSize: 16,
            fontWeight: 'bold',
          } as any,
        },
      ],
    };
  };

  /**
   * 生成折线图配置
   */
  const generateLineOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);

    // 如果配置中已经有series，优先使用配置的series（支持多线图表）
    if (config.options?.series && Array.isArray(config.options.series)) {
      return baseOption; // baseOption已经通过...config.options合并了series
    }

    // 如果配置中已经有xAxis数据，优先使用配置的xAxis
    const xAxisData = (config.options?.xAxis as any)?.data || data.map((item) => item.name);

    return {
      ...baseOption,
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        ...config.options?.xAxis, // 合并用户自定义的xAxis配置
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        splitLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        ...config.options?.yAxis, // 合并用户自定义的yAxis配置
      },
      series: [
        {
          type: 'line',
          data: data.map((item) => ({
            // 保留原始数据的所有属性，特别是children用于数据下探
            ...item,
          })),
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          areaStyle: {
            opacity: 0.3,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
    };
  };

  /**
   * 生成散点图配置
   */
  const generateScatterOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);

    return {
      ...baseOption,
      xAxis: {
        type: 'value',
        name: '销售额(万元)',
        nameLocation: 'middle',
        nameGap: 30,
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        splitLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '利润率(%)',
        nameLocation: 'middle',
        nameGap: 40,
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
        splitLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
      },
      series: [
        {
          type: 'scatter',
          data: data.map((item) => ({
            // @ts-ignore
            name: item.name,
            // @ts-ignore
            value: Array.isArray(item.value) ? item.value : [item.value, 0],
            ...item,
          })),
          symbolSize: 10,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const data = params.data;
          return `${data.name}<br/>销售额: ${data.value[0]}万元<br/>利润率: ${data.value[1]}%`;
        },
      },
    };
  };

  /**
   * 生成漏斗图配置
   */
  const generateFunnelOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);

    return {
      ...baseOption,
      series: [
        {
          type: 'funnel',
          data: data.map((item) => ({
            // @ts-ignore
            name: item.name,
            // @ts-ignore
            value: typeof item.value === 'number' ? item.value : item.value[0],
            ...item,
          })),
          left: '10%',
          top: 60,
          width: '80%',
          height: '60%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside',
            color: '#fff',
            fontSize: 12,
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: 'solid',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        bottom: 10,
        textStyle: {
          color: defaultChartConfig.textColor,
        },
      },
    };
  };

  /**
   * 生成雷达图配置
   */
  const generateRadarOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    const baseOption = generateBaseOption(config);

    // 从第一个数据项获取指标配置
    const indicators = data[0]?.indicators || ['指标1', '指标2', '指标3', '指标4', '指标5', '指标6'];

    return {
      ...baseOption,
      radar: {
        indicator: indicators.map((name) => ({ name, max: 100 })),
        radius: '60%',
        axisLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        splitLine: {
          lineStyle: {
            color: defaultChartConfig.gridLineColor,
          },
        },
        axisLabel: {
          color: defaultChartConfig.textColor,
        },
      },
      series: [
        {
          type: 'radar',
          data: data.map((item) => ({
            name: item.name,
            value: Array.isArray(item.value) ? item.value : [item.value],
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
        },
      ],
      legend: {
        bottom: 10,
        textStyle: {
          color: defaultChartConfig.textColor,
        },
      },
    };
  };

  /**
   * 根据图表类型生成配置
   */
  const generateChartOption = (config: ChartConfig, data: ChartDataItem[]): EChartsOption => {
    switch (config.type) {
      case 'bar':
        return generateBarOption(config, data);
      case 'pie':
        return generatePieOption(config, data);
      case 'line':
        return generateLineOption(config, data);
      case 'scatter':
        return generateScatterOption(config, data);
      case 'funnel':
        return generateFunnelOption(config, data);
      case 'radar':
        return generateRadarOption(config, data);
      default:
        console.warn(`未支持的图表类型: ${config.type}`);
        return generateBaseOption(config);
    }
  };

  return {
    // 状态
    globalConfig,

    // 方法
    generateBaseOption,
    generateBarOption,
    generatePieOption,
    generateLineOption,
    generateScatterOption,
    generateFunnelOption,
    generateRadarOption,
    generateChartOption,
  };
}
