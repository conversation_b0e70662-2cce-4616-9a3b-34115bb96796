import type { App } from 'vue';
import { Icon } from './Icon';
import AIcon from '/@/components/jeecg/AIcon.vue';
import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

import { Button, JUploadButton } from './Button';

// 按需注册antd的组件
import {
  // Need
  Button as AntButton,
  Select,
  Alert,
  Checkbox,
  DatePicker,
  TimePicker,
  Calendar,
  Radio,
  Switch,
  Card,
  List,
  Tabs,
  Descriptions,
  Tree,
  Table,
  Divider,
  Modal,
  Drawer,
  TreeSelect,
  Dropdown,
  Tag,
  Tooltip,
  Badge,
  Popover,
  Upload,
  // Transfer, // 未使用，已移除
  // Steps, // 未使用，已移除
  PageHeader,
  Result,
  Empty,
  Avatar,
  Menu,
  Breadcrumb,
  Form,
  Input,
  Row,
  Col,
  Spin,
  Space,
  Layout,
  Collapse,
  // Slider, // 未使用，已移除
  InputNumber,
  Carousel,
  Popconfirm,
  Skeleton,
  Cascader,
  Rate,
  Progress,
  // Flex, // 未使用，已移除
} from 'ant-design-vue';
const compList = [AntButton.Group, Icon, AIcon, JUploadButton];


export function registerGlobComp(app: App) {
  compList.forEach((comp) => {
    app.component(comp.name || comp.displayName, comp);
  });
  
  // TinyMCE 富文本编辑器 - 改为异步加载以减少主应用体积
  app.component(
    'Tinymce',
    createAsyncComponent(() => import('./Tinymce/src/Editor.vue'), {
      loading: true,
    })
  );

  // 图标选择器 - 异步加载以减少主应用体积
  app.component(
    'IconPicker',
    createAsyncComponent(() => import('./Icon/src/IconPicker.vue'), {
      loading: true,
    })
  );

  // 基础表格 - 异步加载以减少主应用体积
  app.component(
    'BasicTable',
    createAsyncComponent(() => import('./Table/src/BasicTable.vue'), {
      loading: true,
    })
  );
  app.use(Select)
    .use(Alert)
    .use(Button)
    .use(Breadcrumb)
    .use(Checkbox)
    .use(DatePicker)
    .use(TimePicker)
    .use(Calendar)
    .use(Radio)
    .use(Switch)
    .use(Card)
    .use(List)
    .use(Descriptions)
    .use(Tree)
    .use(TreeSelect)
    .use(Table)
    .use(Divider)
    .use(Modal)
    .use(Drawer)
    .use(Dropdown)
    .use(Tag)
    .use(Tooltip)
    .use(Badge)
    .use(Popover)
    .use(Upload)
    // .use(Transfer) // 未使用，已移除
    // .use(Steps) // 未使用，已移除
    .use(PageHeader)
    .use(Result)
    .use(Empty)
    .use(Avatar)
    .use(Menu)
    .use(Tabs)
    .use(Form)
    .use(Input)
    .use(Row)
    .use(Col)
    .use(Spin)
    .use(Space)
    .use(Layout)
    .use(Collapse)
    // .use(Slider) // 未使用，已移除
    .use(InputNumber)
    .use(Carousel)
    .use(Popconfirm)
    .use(Skeleton)
    .use(Cascader)
    .use(Rate)
    .use(Progress)
    // .use(Flex) // 未使用，已移除
    console.log("---初始化---， 全局注册Antd、仪表盘、流程设计器、online、流程等组件--------------")
}
