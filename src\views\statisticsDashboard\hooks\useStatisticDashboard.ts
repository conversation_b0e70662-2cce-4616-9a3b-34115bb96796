/**
 * 统计看板状态管理 Hook
 * 使用 Vue 3 组合式API 管理看板的整体状态
 */

import { ref, reactive, computed, watch, readonly, onMounted } from 'vue';
import type { TabConfig, DashboardState } from '../types/statisticDashboard';
import { getAllTabConfigs } from '../mock/data';
import { usePermission } from '/@/hooks/web/usePermission';

/**
 * 统计看板状态管理
 */
export function useStatisticDashboard() {
  // 基础状态
  const activeTab = ref<string>('');
  const isDragging = ref<boolean>(false);
  const draggedItem = ref<string | null>(null);
  const isExporting = ref<boolean>(false);

  const { hasPermission } = usePermission();

  // Tab配置
  const tabs = ref<TabConfig[]>(getAllTabConfigs());

  console.log('tabs', tabs.value);

  // 图表数据缓存
  const chartDataCache = reactive<Record<string, any>>({});

  // 计算属性：当前激活的Tab配置
  const currentTab = computed(() => {
    return tabs.value.find((tab) => tab.id === activeTab.value);
  });

  // 计算属性：当前Tab的图表配置
  const currentCharts = computed(() => {
    return currentTab.value?.groups.map((group) => group.chartList).flat() || [];
  });

  // 计算属性：看板整体状态
  const dashboardState = computed<DashboardState>(() => ({
    tabs: tabs.value,
    activeTab: activeTab.value,
    chartData: chartDataCache,
    isDragging: isDragging.value,
    draggedItem: draggedItem.value,
    isExporting: isExporting.value,
  }));

  // 计算属性：是否有数据
  const hasData = computed(() => {
    return currentCharts.value.length > 0;
  });

  // 计算属性：加载状态
  const isLoading = computed(() => {
    // 这里可以根据实际需要添加加载状态逻辑
    return false;
  });

  /**
   * 切换Tab
   * @param tabId Tab ID
   */
  const switchTab = (tabId: string) => {
    const targetTab = tabs.value.find((tab) => tab.id === tabId);
    if (targetTab) {
      activeTab.value = tabId;
    }
  };

  /**
   * 更新图表数据缓存
   * @param chartId 图表ID
   * @param data 图表数据
   */
  const updateChartData = (chartId: string, data: any) => {
    chartDataCache[chartId] = data;
  };

  /**
   * 获取图表数据
   * @param chartId 图表ID
   */
  const getChartData = (chartId: string) => {
    return chartDataCache[chartId];
  };

  /**
   * 清除图表数据缓存
   * @param chartId 可选，指定清除的图表ID，不传则清除所有
   */
  const clearChartDataCache = (chartId?: string) => {
    if (chartId) {
      delete chartDataCache[chartId];
    } else {
      Object.keys(chartDataCache).forEach((key) => {
        delete chartDataCache[key];
      });
    }
  };

  /**
   * 开始拖拽
   * @param itemId 拖拽项ID
   */
  const startDragging = (itemId: string) => {
    isDragging.value = true;
    draggedItem.value = itemId;
  };

  /**
   * 结束拖拽
   */
  const endDragging = () => {
    isDragging.value = false;
    draggedItem.value = null;
  };

  /**
   * 开始导出
   */
  const startExporting = () => {
    isExporting.value = true;
  };

  /**
   * 结束导出
   */
  const endExporting = () => {
    isExporting.value = false;
  };

  /**
   * 刷新当前Tab数据
   */
  const refreshCurrentTab = async () => {
    if (currentTab.value) {
      // 清除当前Tab相关的图表数据缓存
      currentCharts.value.forEach((chart) => {
        clearChartDataCache(chart.id);
      });

      // 这里可以添加重新获取数据的逻辑
      console.log(`刷新Tab: ${currentTab.value.name}`);
    }
  };

  onMounted(() => {
    tabs.value = tabs.value.filter((tab) => {
      const exist = hasPermission(tab.auth);
      // 寻找第一个有权限的tab
      if (exist && !activeTab.value) {
        activeTab.value = tab.id;
      }
      return exist;
    });
  });

  // 监听Tab切换
  watch(
    () => activeTab.value,
    (newTabId, oldTabId) => {
      console.log(`Tab切换: ${oldTabId} -> ${newTabId}`);
      // 这里可以添加Tab切换时的处理逻辑
    }
  );

  return {
    // 状态
    activeTab: readonly(activeTab),
    isDragging: readonly(isDragging),
    draggedItem: readonly(draggedItem),
    isExporting: readonly(isExporting),
    tabs: tabs,

    // 计算属性
    currentTab,
    currentCharts,
    dashboardState,
    hasData,
    isLoading,

    // 方法
    switchTab,
    updateChartData,
    getChartData,
    clearChartDataCache,
    startDragging,
    endDragging,
    startExporting,
    endExporting,
    refreshCurrentTab,
  };
}

/**
 * 看板布局管理
 */
export function useDashboardLayout() {
  // 自适应布局配置
  const layoutConfig = reactive({
    columns: 2, // 参考列数（不再固定使用）
    gap: 16, // 间距
    minChartWidth: 400, // 最小图表宽度
    maxChartWidth: 600, // 单列时的最大图表宽度
    singleColumnMaxWidth: 650, // 单列布局时的最大宽度
  });

  /**
   * 获取图表容器样式
   */
  const getChartContainerStyle = computed(() => ({
    display: 'grid',
    gridTemplateColumns: `repeat(auto-fit, minmax(${layoutConfig.minChartWidth}px, 1fr))`,
    gap: `${layoutConfig.gap}px`,
    padding: `${layoutConfig.gap}px`,
    maxWidth: '100%',
  }));

  // 简化的调整布局函数（保持接口兼容性）
  const adjustLayout = () => {
    // 不再进行响应式调整，保持固定布局
  };

  return {
    layoutConfig: readonly(layoutConfig),
    adjustLayout,
    getChartContainerStyle,
  };
}
