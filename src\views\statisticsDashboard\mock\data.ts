/**
 * 统计看板模拟数据
 * 包含支持数据下探的层级数据结构
 */

import type { ChartDataItem, ChartConfig, TabConfig } from '../types/statisticDashboard';
import {
  clueEffectivenessChartConfigs,
  clueEffectivenessData,
  sourceOfAllCluesData,
  sourceOfCluesChartConfigs,
  sourceOfEffectiveCluesData,
  utmChartConfigs,
  utmMediumData,
  utmSourceData,
} from './clueSourceMock';
import { followUpOnCluesChartConfigs, followUpOnCluesData, firstFollowUpTimeAnalysisData, firstResponseTimeoutData } from './followUpOnCluesMock';
import { AuthEnum } from '/@/enums/authEnum';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * Tab配置
 */
export const tabConfigs: TabConfig[] = [
  {
    id: 'clueStatistics',
    name: t('clueStatistics'),
    layout: 'grid',
    groups: [
      {
        title: t('overviewOfClues'),
        statisticsList: [
          { title: t('totalNumberOfClues'), total: 1990, increase: 10, decrease: 23.3, core: true },
          { title: t('totalNumberOfEffectiveClues'), total: 1990, increase: 10, decrease: 10, core: false },
          { title: t('clueTradingVolume'), total: 1990, increase: 10, decrease: 10, core: false },
          { title: t('leadFollowUpRate'), total: 1990, increase: 10, decrease: 10, core: false },
          { title: t('cluesAreEfficient'), total: 1990, increase: 10, decrease: 10, core: false },
          { title: t('clueWinRate'), total: 1990, increase: 10, decrease: 10, core: false },
        ],
        chartList: [],
      },
      {
        title: t('sourceOfClues'),
        chartList: [...sourceOfCluesChartConfigs, ...utmChartConfigs, ...clueEffectivenessChartConfigs],
      },
      {
        title: '线索跟进',
        chartList: [...followUpOnCluesChartConfigs],
      },
      // {
      //   title: '线索转化',
      //   chartList: [],
      //   // chartList: [
      //   //   chartConfigs[0], // sales-bar
      //   //   chartConfigs[1], // category-pie
      //   //   chartConfigs[2], // trend-line
      //   //   chartConfigs[3], // sales-scatter
      //   //   chartConfigs[4], // product-radar
      //   //   chartConfigs[5], // conversion-funnel
      //   //   chartConfigs[6], // trend-area
      //   //   chartConfigs[7], // sales-heatmap
      //   // ],
      // },
    ],
    auth: AuthEnum.View_Clue_Statistics,
  },
  {
    id: 'workOrderStatistics',
    name: t('workOrderStatistics'),
    layout: 'grid',
    groups: [],
    auth: AuthEnum.View_Work_Order_Statistics,
  },
  {
    id: 'vehicleStatistics',
    name: t('vehicleStatistics'),
    layout: 'grid',
    groups: [],
    auth: AuthEnum.View_Vehicle_Statistics,
  },
  {
    id: 'userStatistics',
    name: t('userStatistics'),
    layout: 'grid',
    groups: [],
    auth: AuthEnum.View_User_Statistics,
  },
];

/**
 * 数据映射
 */
export const dataMap = {
  // sales: salesData,
  // category: categoryData,
  // trend: monthlyTrendData,
  // scatter: scatterData,
  // radar: radarData,
  // funnel: funnelData,
  // area: areaData,
  // heatmap: heatmapData,
  // gauge: gaugeData,
  sourceOfAllClues: sourceOfAllCluesData, // 全量线索
  sourceOfEffectiveClues: sourceOfEffectiveCluesData, // 有效线索
  utmSource: utmSourceData,
  utmMedium: utmMediumData,
  clueEffectiveness: clueEffectivenessData,
  followUpOnClues: followUpOnCluesData, // 线索跟进状态
  firstFollowUpTimeAnalysis: firstFollowUpTimeAnalysisData, // 首次跟进时长分析
  firstResponseTimeout: firstResponseTimeoutData, // 首次响应超时
};

/**
 * 获取图表数据
 */
export const getChartData = (dataSource: string): ChartDataItem[] => {
  return dataMap[dataSource as keyof typeof dataMap] || [];
};

/**
 * 获取线索来源数据
 */
export const getSourceOfCluesData = (type: 'allClues' | 'effectiveClues' = 'allClues') => {
  return type === 'allClues' ? sourceOfAllCluesData : sourceOfEffectiveCluesData;
};

/**
 * 获取图表配置（更新以支持线索跟进状态图表）
 */
export const getChartConfig = (chartId: string): ChartConfig | undefined => {
  // 从所有可用的图表配置中查找
  const allConfigs = [...sourceOfCluesChartConfigs, ...utmChartConfigs, ...clueEffectivenessChartConfigs, ...followUpOnCluesChartConfigs];
  return allConfigs.find((config) => config.id === chartId);
};

/**
 * 获取Tab配置
 */
export const getTabConfig = (tabId: string): TabConfig | undefined => {
  return tabConfigs.find((config) => config.id === tabId);
};

/**
 * 获取所有Tab配置
 */
export const getAllTabConfigs = (): TabConfig[] => {
  return tabConfigs;
};
