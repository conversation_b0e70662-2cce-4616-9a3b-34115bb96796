/**
 * 统计看板样式文件
 * 定义看板组件的通用样式和主题
 */

// 看板容器样式
.statistics-dashboard {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;

  // 筛选面板样式
  .filter-panel {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;

    .filter-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #333;
    }

    .filter-controls {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .filter-label {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  // Tab容器样式
  .tab-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .ant-tabs {
      .ant-tabs-tab {
        font-weight: 500;
        
        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #1890ff;
          }
        }
      }

      .ant-tabs-content-holder {
        padding: 16px;
      }
    }
  }

  // 图表网格布局
  .chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-top: 16px;

    // 当容器宽度较小时（单列布局），限制图表最大宽度
    @media (max-width: 850px) {
      grid-template-columns: 1fr;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }

    // 超小屏幕适配
    @media (max-width: 600px) {
      max-width: 100%;
      margin-left: 0;
      margin-right: 0;
      padding: 0 8px;
    }
  }

  // 图表组样式
  .chart-group {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .chart-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin: 0;
      }

      .chart-actions {
        display: flex;
        gap: 8px;
      }
    }

    .chart-content {
      position: relative;
      
      .chart-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        z-index: 10;
      }
    }

    .chart-footer {
      padding: 12px 16px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      font-size: 12px;
      color: #666;

      .drill-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .current-level {
          font-weight: 500;
        }

        .breadcrumb {
          color: #999;
        }
      }
    }
  }
}

// 拖拽相关样式
.chart-dragging {
  .chart-group {
    opacity: 0.6;
    transform: rotate(2deg);
    cursor: grabbing;
  }
}

.drag-handle {
  cursor: grab;
  user-select: none;
  
  &:active {
    cursor: grabbing;
  }

  .drag-icon {
    color: #999;
    transition: color 0.3s ease;
  }

  &:hover .drag-icon {
    color: #666;
  }
}

// 导出模式样式
.pdf-export-mode {
  .chart-group {
    box-shadow: none;
    border: 1px solid #e8e8e8;
    page-break-inside: avoid;
  }

  .drag-handle {
    display: none;
  }

  .chart-actions {
    display: none;
  }
}

// 移除了响应式设计相关的媒体查询

// 主题变量
:root {
  --dashboard-primary-color: #1890ff;
  --dashboard-success-color: #52c41a;
  --dashboard-warning-color: #fa8c16;
  --dashboard-error-color: #ff4d4f;
  --dashboard-bg-color: #f5f5f5;
  --dashboard-card-bg: #ffffff;
  --dashboard-border-color: #e8e8e8;
  --dashboard-text-color: #333333;
  --dashboard-text-secondary: #666666;
  --dashboard-text-disabled: #999999;
}

// 暗色主题支持
[data-theme='dark'] {
  --dashboard-bg-color: #141414;
  --dashboard-card-bg: #1f1f1f;
  --dashboard-border-color: #303030;
  --dashboard-text-color: #ffffff;
  --dashboard-text-secondary: #a6a6a6;
  --dashboard-text-disabled: #737373;

  .statistics-dashboard {
    background: var(--dashboard-bg-color);

    .filter-panel,
    .tab-container,
    .chart-group {
      background: var(--dashboard-card-bg);
      border-color: var(--dashboard-border-color);
    }

    .chart-title,
    .filter-title {
      color: var(--dashboard-text-color);
    }

    .filter-label,
    .chart-footer {
      color: var(--dashboard-text-secondary);
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.chart-enter-active {
  animation: fadeInUp 0.3s ease-out;
}

.chart-leave-active {
  animation: fadeInUp 0.3s ease-out reverse;
}

.filter-enter-active {
  animation: slideInRight 0.3s ease-out;
}

// 工具提示样式
.chart-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  
  .tooltip-title {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .tooltip-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }
}
