/**
 * 图表性能优化Hook
 * 提供图表渲染性能监控、优化和用户体验改善功能
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { debounce, throttle } from 'lodash-es';

/**
 * 性能指标接口
 */
interface PerformanceMetrics {
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 数据处理时间（毫秒） */
  dataProcessTime: number;
  /** 内存使用量（MB） */
  memoryUsage: number;
  /** 帧率 */
  fps: number;
  /** 错误次数 */
  errorCount: number;
  /** 最后更新时间 */
  lastUpdate: Date;
}

/**
 * 性能配置接口
 */
interface PerformanceConfig {
  /** 是否启用性能监控 */
  enableMonitoring: boolean;
  /** 是否启用自动优化 */
  enableAutoOptimization: boolean;
  /** 最大数据点数量 */
  maxDataPoints: number;
  /** 渲染防抖延迟 */
  renderDebounceDelay: number;
  /** 窗口大小变化节流延迟 */
  resizeThrottleDelay: number;
}

/**
 * 图表性能优化Hook
 */
export function useChartPerformance(config: Partial<PerformanceConfig> = {}) {
  // 默认配置
  const defaultConfig: PerformanceConfig = {
    enableMonitoring: true,
    enableAutoOptimization: true,
    maxDataPoints: 1000,
    renderDebounceDelay: 100,
    resizeThrottleDelay: 200,
  };

  const performanceConfig = reactive({ ...defaultConfig, ...config });

  // 性能指标
  const metrics = reactive<PerformanceMetrics>({
    renderTime: 0,
    dataProcessTime: 0,
    memoryUsage: 0,
    fps: 0,
    errorCount: 0,
    lastUpdate: new Date(),
  });

  // 性能历史记录
  const performanceHistory = ref<PerformanceMetrics[]>([]);

  // 是否正在监控
  const isMonitoring = ref(false);

  // FPS 计算相关
  let frameCount = 0;
  let lastTime = performance.now();
  let animationFrameId: number | null = null;

  /**
   * 开始性能监控
   */
  const startMonitoring = () => {
    if (!performanceConfig.enableMonitoring || isMonitoring.value) return;

    isMonitoring.value = true;
    startFPSMonitoring();
    startMemoryMonitoring();
  };

  /**
   * 停止性能监控
   */
  const stopMonitoring = () => {
    isMonitoring.value = false;

    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };

  /**
   * 开始FPS监控
   */
  const startFPSMonitoring = () => {
    const calculateFPS = () => {
      frameCount++;
      const currentTime = performance.now();

      if (currentTime - lastTime >= 1000) {
        metrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        frameCount = 0;
        lastTime = currentTime;
      }

      if (isMonitoring.value) {
        animationFrameId = requestAnimationFrame(calculateFPS);
      }
    };

    calculateFPS();
  };

  /**
   * 开始内存监控
   */
  const startMemoryMonitoring = () => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        metrics.memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      }
    };

    // 每5秒更新一次内存使用量
    const memoryInterval = setInterval(() => {
      if (isMonitoring.value) {
        updateMemoryUsage();
      } else {
        clearInterval(memoryInterval);
      }
    }, 5000);
  };

  /**
   * 记录渲染时间
   */
  const recordRenderTime = (startTime: number, endTime: number) => {
    metrics.renderTime = endTime - startTime;
    metrics.lastUpdate = new Date();

    // 保存历史记录
    if (performanceHistory.value.length > 100) {
      performanceHistory.value.shift();
    }
    performanceHistory.value.push({ ...metrics });
  };

  /**
   * 记录数据处理时间
   */
  const recordDataProcessTime = (startTime: number, endTime: number) => {
    metrics.dataProcessTime = endTime - startTime;
  };

  /**
   * 记录错误
   */
  const recordError = (error: Error) => {
    metrics.errorCount++;
    console.error('图表性能监控捕获错误:', error);
  };

  /**
   * 数据优化 - 减少数据点数量
   */
  const optimizeData = <T extends { value: number }>(data: T[]): T[] => {
    if (!performanceConfig.enableAutoOptimization) return data;

    const maxPoints = performanceConfig.maxDataPoints;
    if (data.length <= maxPoints) return data;

    // 使用采样算法减少数据点
    const step = Math.ceil(data.length / maxPoints);
    return data.filter((_, index) => index % step === 0);
  };

  /**
   * 创建防抖渲染函数
   */
  const createDebouncedRender = (renderFn: (...args: any[]) => any) => {
    return debounce(renderFn, performanceConfig.renderDebounceDelay);
  };

  /**
   * 创建节流窗口大小变化处理函数
   */
  const createThrottledResize = (resizeFn: (...args: any[]) => any) => {
    return throttle(resizeFn, performanceConfig.resizeThrottleDelay);
  };

  /**
   * 性能评分计算
   */
  const performanceScore = computed(() => {
    let score = 100;

    // 渲染时间评分 (期望 < 16ms)
    if (metrics.renderTime > 16) {
      score -= Math.min(30, (metrics.renderTime - 16) / 2);
    }

    // FPS评分 (期望 >= 30)
    if (metrics.fps < 30) {
      score -= Math.min(25, (30 - metrics.fps) * 2);
    }

    // 内存使用评分 (期望 < 100MB)
    if (metrics.memoryUsage > 100) {
      score -= Math.min(20, (metrics.memoryUsage - 100) / 10);
    }

    // 错误次数评分
    score -= Math.min(25, metrics.errorCount * 5);

    return Math.max(0, Math.round(score));
  });

  /**
   * 性能等级
   */
  const performanceLevel = computed(() => {
    const score = performanceScore.value;
    if (score >= 90) return { level: 'excellent', color: '#52c41a', text: '优秀' };
    if (score >= 70) return { level: 'good', color: '#1890ff', text: '良好' };
    if (score >= 50) return { level: 'fair', color: '#faad14', text: '一般' };
    return { level: 'poor', color: '#ff4d4f', text: '较差' };
  });

  /**
   * 获取性能建议
   */
  const getPerformanceSuggestions = (): string[] => {
    const suggestions: string[] = [];

    if (metrics.renderTime > 16) {
      suggestions.push('渲染时间过长，建议减少数据点数量或简化图表配置');
    }

    if (metrics.fps < 30) {
      suggestions.push('帧率较低，建议关闭不必要的动画效果');
    }

    if (metrics.memoryUsage > 100) {
      suggestions.push('内存使用量较高，建议清理不必要的数据缓存');
    }

    if (metrics.errorCount > 0) {
      suggestions.push('存在错误，请检查图表配置和数据格式');
    }

    return suggestions;
  };

  /**
   * 重置性能指标
   */
  const resetMetrics = () => {
    metrics.renderTime = 0;
    metrics.dataProcessTime = 0;
    metrics.memoryUsage = 0;
    metrics.fps = 0;
    metrics.errorCount = 0;
    metrics.lastUpdate = new Date();
    performanceHistory.value = [];
  };

  // 组件挂载时开始监控
  onMounted(() => {
    startMonitoring();
  });

  // 组件卸载时停止监控
  onUnmounted(() => {
    stopMonitoring();
  });

  return {
    // 配置
    performanceConfig,

    // 状态
    metrics,
    performanceHistory,
    isMonitoring,

    // 计算属性
    performanceScore,
    performanceLevel,

    // 方法
    startMonitoring,
    stopMonitoring,
    recordRenderTime,
    recordDataProcessTime,
    recordError,
    optimizeData,
    createDebouncedRender,
    createThrottledResize,
    getPerformanceSuggestions,
    resetMetrics,
  };
}
