<template>
  <div class="login_box">
    <div class="input_box">
      <div class="input_item">
        <div class="input_item_label">
          <span>{{ t('authAccount') }}</span>
        </div>
        <div class="input_item_input">
          <input type="text" v-model="formData.account" autocomplete="off" />
        </div>
      </div>
      <div class="input_item">
        <div class="input_item_label">
          <span>{{ t('authPassword') }}</span>
        </div>
        <div class="input_item_input">
          <input type="password" v-model="formData.password" autocomplete="off" />
        </div>
      </div>
      <div class="input_item">
        <div class="input_item_label">
          <span>{{ t('email') }}</span>
        </div>
        <div class="input_item_input">
          <input type="text" v-model="formData.email" autocomplete="off" />
        </div>
      </div>
      <div class="input_item">
        <div class="input_item_label">
          <span>{{ t('inputCode') }}</span>
        </div>
        <div class="input_item_input send_sms flex">
          <input type="text" v-model="formData.code" autocomplete="off" />
          <Button type="primary" v-if="!isShowDjs" @click.stop="sendSms()">{{ t('send_verification_code') }}</Button>
          <count-down :time="djsTime" v-else @finish="isShowDjs = false">
            <template #default="timeData">
              <div class="djs_item">{{ timeData.seconds }}</div>
            </template>
          </count-down>
        </div>
      </div>
      <div class="input_item">
        <div class="input_item_label">
          <span>{{ t('name') }}</span>
        </div>
        <div class="input_item_input">
          <input type="text" v-model="formData.userName" autocomplete="off" />
        </div>
      </div>
      <div class="input_item">
        <Checkbox v-model="isAgree">
          <div class="flex" style="flex-wrap: wrap">
            {{ t('i_have_read_and_agree') }}
            <div class="link_text" :underline="false" type="primary" @click.stop="viewXieyi(1)">{{ `《${t('userAgreement')}》` }}</div>
            {{ t('and') }}
            <div class="link_text" :underline="false" type="primary" @click.stop="viewXieyi(2)">{{ `《${t('privacy_policy')}》` }}</div>
          </div>
        </Checkbox>
      </div>
    </div>
    <Button style="margin-bottom: 50px" type="primary" @click="handleSubmit()">{{ t('completeActivation') }}</Button>
  </div>
</template>
<script lang="ts" setup name="h5Login">
  import { ref, onMounted } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useRouter } from 'vue-router';
  import { showToast, Checkbox, CountDown, Button } from 'vant';
  import 'vant/lib/index.css';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useUserStore } from '/@/store/modules/user';
  import { EmailRegex } from '/@/utils/helper/validator';
  import { activationAccountSendCodeApi, doneActivationAccountReqApi } from '/@/api/h5Api/loginXiangguan';
  import rsaEncryptor from '/@/utils/rsaEncrypt';

  const { t } = useI18n('common');

  const router = useRouter();

  const userStore = useUserStore();

  // 查看协议
  const viewXieyi = async (type: number) => {
    // 设置当前填写的信息缓存
    localStorage.setItem('set_account_info', JSON.stringify(formData.value));
    router.push({
      path: PageEnum.H5_DOC_ONLINE,
      query: {
        viewDocType: type,
      },
    });
  };

  // 表单
  const formData = ref({
    email: '',
    code: '',
    userName: '',
    // 账号-激活账号页面传入
    account: '',
    // 密码-激活账号页面传入
    password: '',
  });

  // 是否统一协议
  const isAgree = ref(false);

  // 倒计时
  const djsTime = ref(60 * 1000);
  // 是否显示倒计时
  const isShowDjs = ref(false);

  // 发送验证码
  const sendSms = async () => {
    if (!EmailRegex.test(formData.value.email)) {
      showToast({ message: t('enterFormatEmail') });
      return;
    }
    if (loading.value) return;
    loading.value = true;
    try {
      await activationAccountSendCodeApi({
        account: formData.value.email,
      });
      isShowDjs.value = true;
      loading.value = false;
    } catch (err) {
      loading.value = false;
    }
  };

  // 完成激活提交
  let loading = ref(false);
  const handleSubmit = async () => {
    if (!formData.value.account) {
      showToast({ message: t('pleaseEnterAuthAccount') });
      return;
    }
    if (!formData.value.password) {
      showToast({ message: t('pleaseEnterAuthPassword') });
      return;
    }
    if (!EmailRegex.test(formData.value.email)) {
      showToast({ message: t('enterFormatEmail') });
      return;
    }
    if (!formData.value.code) {
      showToast({ message: t('inputCodePlaceholder') });
      return;
    }
    if (!formData.value.userName) {
      showToast({ message: t('pleaseEnterName') });
      return;
    }
    if (!isAgree.value) {
      showToast({ message: t('pleaseAgreeProtocol') });
      return;
    }
    try {
      if (loading.value) return;
      loading.value = true;
      await doneActivationAccountReqApi({
        ...formData.value,
      });
      await userStore.login({
        account: formData.value.email,
        password: rsaEncryptor.encrypt(formData.value.password) as string,
        isH5: true,
      });
      loading.value = false;
      showToast({ message: t('accountActivationSuccess') });
    } catch (err) {
      loading.value = false;
    }
  };

  onMounted(() => {
    const setAccountInfo = localStorage.getItem('set_account_info');
    if (setAccountInfo) {
      formData.value = JSON.parse(setAccountInfo);
      // 删除缓存
      localStorage.removeItem('set_account_info');
    }
  });
</script>
<style scoped lang="less">
  .h5_login {
    width: 60px;
  }
  .h5_login_tabs {
    margin-top: 36px;
  }

  ::v-deep(.van-tabs__nav--card) {
    margin: 0;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }
  .send_sms ::v-deep(.van-button) {
    width: 186px;
    margin-left: 10px;
  }
  .djs_item {
    width: 56px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    background-color: #1989fa;
    margin-left: 10px;
    border-radius: 4px;
  }
  .input_box {
    margin-top: 30px;
    .input_item {
      margin-bottom: 20px;
      .input_item_label {
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
      }
      .input_item_input {
        input {
          width: 100%;
          height: 40px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          padding: 0 10px;
        }
      }
    }
  }
  .link_text {
    color: var(--van-button-primary-background);
  }
</style>
