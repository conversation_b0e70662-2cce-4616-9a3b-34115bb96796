const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * 构建性能分析脚本
 */
class BuildAnalyzer {
  constructor() {
    this.logFile = 'build-analysis.log';
    this.resultsFile = 'build-results.json';
  }

  /**
   * 执行构建分析
   */
  async analyze() {
    console.log('🔍 开始构建性能分析...\n');
    
    try {
      // 清理缓存
      await this.cleanCache();
      
      // 执行构建并记录
      const buildResult = await this.runBuild();
      
      // 分析结果
      const analysis = await this.analyzeResults(buildResult);
      
      // 保存结果
      await this.saveResults(analysis);
      
      // 显示报告
      this.displayReport(analysis);
      
    } catch (error) {
      console.error('❌ 分析过程中出现错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 清理缓存
   */
  async cleanCache() {
    console.log('🧹 清理构建缓存...');
    
    const cacheDirs = [
      'node_modules/.vite',
      'dist',
      '.tsbuildinfo'
    ];
    
    cacheDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`  ✅ 已清理: ${dir}`);
      }
    });
    
    console.log('');
  }

  /**
   * 执行构建
   */
  async runBuild() {
    console.log('🚀 开始构建...');
    
    const startTime = Date.now();
    
    try {
      // 执行构建命令并捕获输出
      const output = execSync('pnpm build', {
        encoding: 'utf8',
        stdio: 'pipe',
        maxBuffer: 1024 * 1024 * 10 // 10MB buffer
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 保存构建日志
      fs.writeFileSync(this.logFile, output);
      
      console.log(`✅ 构建完成，耗时: ${duration}ms (${(duration / 1000).toFixed(2)}s)\n`);
      
      return {
        success: true,
        duration,
        output,
        startTime,
        endTime
      };
      
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.error(`❌ 构建失败，耗时: ${duration}ms`);
      console.error('错误输出:', error.stdout || error.message);
      
      return {
        success: false,
        duration,
        output: error.stdout || error.message,
        error: error.message,
        startTime,
        endTime
      };
    }
  }

  /**
   * 分析构建结果
   */
  async analyzeResults(buildResult) {
    console.log('📊 分析构建结果...');
    
    const analysis = {
      buildTime: buildResult.duration,
      success: buildResult.success,
      timestamp: new Date().toISOString(),
      phases: this.analyzePhases(buildResult.output),
      bundles: this.analyzeBundles(),
      dependencies: this.analyzeDependencies(buildResult.output),
      performance: this.analyzePerformance(buildResult)
    };
    
    return analysis;
  }

  /**
   * 分析构建阶段
   */
  analyzePhases(output) {
    const phases = {};
    const lines = output.split('\n');
    
    // 查找各阶段的时间信息
    const phasePatterns = {
      '依赖预构建': /pre-bundling.*?(\d+)ms/i,
      'TypeScript编译': /typescript.*?(\d+)ms/i,
      'Vue编译': /vue.*?(\d+)ms/i,
      '代码转换': /transform.*?(\d+)ms/i,
      '打包': /bundle.*?(\d+)ms/i,
      '压缩': /minify.*?(\d+)ms/i,
      '写入文件': /write.*?(\d+)ms/i
    };
    
    lines.forEach(line => {
      Object.entries(phasePatterns).forEach(([phase, pattern]) => {
        const match = line.match(pattern);
        if (match) {
          phases[phase] = parseInt(match[1]);
        }
      });
    });
    
    return phases;
  }

  /**
   * 分析构建产物
   */
  analyzeBundles() {
    const distPath = path.join(process.cwd(), 'dist');
    if (!fs.existsSync(distPath)) {
      return { error: 'dist 目录不存在' };
    }
    
    const bundles = [];
    
    const analyzeDir = (dir, basePath = '') => {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const relativePath = path.join(basePath, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          analyzeDir(fullPath, relativePath);
        } else {
          bundles.push({
            name: relativePath,
            size: stats.size,
            type: path.extname(item).slice(1) || 'unknown'
          });
        }
      });
    };
    
    analyzeDir(distPath);
    
    // 按大小排序
    bundles.sort((a, b) => b.size - a.size);
    
    const totalSize = bundles.reduce((sum, bundle) => sum + bundle.size, 0);
    const jsFiles = bundles.filter(b => b.type === 'js');
    const cssFiles = bundles.filter(b => b.type === 'css');
    const assetFiles = bundles.filter(b => !['js', 'css', 'html'].includes(b.type));
    
    return {
      total: bundles.length,
      totalSize,
      jsFiles: jsFiles.length,
      cssFiles: cssFiles.length,
      assetFiles: assetFiles.length,
      largest: bundles.slice(0, 10),
      breakdown: {
        js: jsFiles.reduce((sum, f) => sum + f.size, 0),
        css: cssFiles.reduce((sum, f) => sum + f.size, 0),
        assets: assetFiles.reduce((sum, f) => sum + f.size, 0)
      }
    };
  }

  /**
   * 分析依赖信息
   */
  analyzeDependencies(output) {
    const dependencies = {};
    const lines = output.split('\n');
    
    // 查找依赖相关的时间信息
    lines.forEach(line => {
      // 匹配依赖加载时间
      const depMatch = line.match(/(\w+(?:[-@/]\w+)*):.*?(\d+)ms/);
      if (depMatch) {
        const [, depName, time] = depMatch;
        dependencies[depName] = (dependencies[depName] || 0) + parseInt(time);
      }
    });
    
    // 转换为数组并排序
    const sortedDeps = Object.entries(dependencies)
      .map(([name, time]) => ({ name, time }))
      .sort((a, b) => b.time - a.time)
      .slice(0, 10);
    
    return {
      total: Object.keys(dependencies).length,
      slowest: sortedDeps,
      totalTime: Object.values(dependencies).reduce((sum, time) => sum + time, 0)
    };
  }

  /**
   * 分析性能指标
   */
  analyzePerformance(buildResult) {
    const performance = {
      buildTime: buildResult.duration,
      buildSpeed: 'unknown'
    };
    
    // 构建速度评级
    if (buildResult.duration < 10000) {
      performance.buildSpeed = 'excellent';
    } else if (buildResult.duration < 20000) {
      performance.buildSpeed = 'good';
    } else if (buildResult.duration < 30000) {
      performance.buildSpeed = 'fair';
    } else {
      performance.buildSpeed = 'poor';
    }
    
    return performance;
  }

  /**
   * 保存分析结果
   */
  async saveResults(analysis) {
    fs.writeFileSync(this.resultsFile, JSON.stringify(analysis, null, 2));
    console.log(`📄 分析结果已保存到: ${this.resultsFile}\n`);
  }

  /**
   * 显示分析报告
   */
  displayReport(analysis) {
    console.log('📋 构建性能报告');
    console.log('='.repeat(50));
    
    // 基本信息
    console.log(`\n⏱️ 构建时间: ${analysis.buildTime}ms (${(analysis.buildTime / 1000).toFixed(2)}s)`);
    console.log(`📅 分析时间: ${analysis.timestamp}`);
    console.log(`✅ 构建状态: ${analysis.success ? '成功' : '失败'}`);
    
    // 构建产物信息
    if (analysis.bundles && !analysis.bundles.error) {
      console.log(`\n📦 构建产物:`);
      console.log(`  总文件数: ${analysis.bundles.total}`);
      console.log(`  总大小: ${this.formatSize(analysis.bundles.totalSize)}`);
      console.log(`  JS文件: ${analysis.bundles.jsFiles} (${this.formatSize(analysis.bundles.breakdown.js)})`);
      console.log(`  CSS文件: ${analysis.bundles.cssFiles} (${this.formatSize(analysis.bundles.breakdown.css)})`);
      console.log(`  资源文件: ${analysis.bundles.assetFiles} (${this.formatSize(analysis.bundles.breakdown.assets)})`);
    }
    
    // 性能评级
    console.log(`\n🎯 性能评级: ${this.getPerformanceEmoji(analysis.performance.buildSpeed)} ${analysis.performance.buildSpeed.toUpperCase()}`);
    
    // 建议
    this.displayRecommendations(analysis);
  }

  /**
   * 显示优化建议
   */
  displayRecommendations(analysis) {
    console.log('\n💡 优化建议:');
    
    if (analysis.buildTime > 30000) {
      console.log('  ⚠️ 构建时间过长，建议检查大型依赖和插件配置');
    }
    
    if (analysis.bundles && analysis.bundles.totalSize > 5 * 1024 * 1024) {
      console.log('  ⚠️ 构建产物较大，建议进一步优化代码分割');
    }
    
    if (analysis.dependencies && analysis.dependencies.slowest.length > 0) {
      console.log('  📦 最慢的依赖:');
      analysis.dependencies.slowest.slice(0, 3).forEach(dep => {
        console.log(`    - ${dep.name}: ${dep.time}ms`);
      });
    }
  }

  /**
   * 格式化文件大小
   */
  formatSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  }

  /**
   * 获取性能表情符号
   */
  getPerformanceEmoji(speed) {
    const emojis = {
      excellent: '🚀',
      good: '✅',
      fair: '⚠️',
      poor: '❌'
    };
    return emojis[speed] || '❓';
  }
}

// 执行分析
if (require.main === module) {
  const analyzer = new BuildAnalyzer();
  analyzer.analyze().catch(console.error);
}

module.exports = BuildAnalyzer;
